# Simple Database System

A lightweight, secure key-value database system for storing game configuration and runtime data in `.db` files.

## Overview

The Simple Database System replaces the old INI-style configuration files with a more consistent and secure approach. It stores key-value pairs in text files with the `.db` extension using a simple, readable format.

## Features

- **Simple text-based format**: `(registration (Key %d)(Value %d))`
- **Security integration**: Uses SecureBuffer and MemoryGuard components
- **Automatic file management**: Creates directories and handles file operations
- **Thread-safe operations**: Built-in locking for concurrent access
- **Input validation**: Bounds checking for keys and values
- **Easy integration**: Helper functions for common operations

## File Format

Each `.db` file contains lines in the following format:
```
(registration (Key 123)(Value 456))
(registration (Key 789)(Value 101112))
```

- **Key range**: 0 to 99,999
- **Value range**: 0 to 999,999
- **Location**: `./HethCFG/Data/systemname.db`

## Usage Examples

### Basic Usage

```cpp
#include "SimpleDatabase.h"

// Create a database for a specific system
SimpleDatabase db = SimpleDatabase::create_for_system("killtosummon");

// Save data
db.set(123, 456);  // Key 123 = Value 456

// Read data
int value = db.get(123, 0);  // Returns 456, or 0 if key doesn't exist

// Check if key exists
bool exists = db.has_key(123);  // Returns true

// Remove data
bool removed = db.remove(123);  // Returns true if key was removed
```

### Helper Functions (Recommended)

```cpp
// Save data to any system database
bool success = SaveToSystemDB("killtosummon", 123, 456);

// Read data from any system database
int value = ReadFromSystemDB("killtosummon", 123, 0);  // Default to 0

// Check if key exists
bool exists = KeyExistsInSystemDB("killtosummon", 123);

// Remove key
bool removed = RemoveFromSystemDB("killtosummon", 123);
```

## Integration with Existing Systems

### KillToSummon System (Already Implemented)

**Before (Core.cpp):**
```cpp
void saveKilled(int minionIndex, int minionAmount) {
    std::string filename = "./HethCFG/Data/killtosummon_" + Int2String(minionIndex) + ".txt";
    std::string content = "[" + Int2String(minionIndex) + "]\nAmount = " + Int2String(minionAmount);
    // ... file writing code ...
}
```

**After (Core.cpp):**
```cpp
void saveKilled(int minionIndex, int minionAmount) {
    if (!SaveToSystemDB("killtosummon", minionIndex, minionAmount)) {
        writeToFile("Error saving kill data");
    }
}
```

**Before (ReadConfig.h):**
```cpp
std::string path = "./HethCFG/Data/killtosummon_"+mindex+".txt";
totalKilled = GetPrivateProfileIntA(mindex.c_str(), "Amount", 0, path.c_str());
```

**After (ReadConfig.h):**
```cpp
totalKilled = ReadFromSystemDB("killtosummon", minionIndex, 0);
```

### Adding New Systems

1. **Choose a system name** (e.g., "bossdonation", "playerstats", "questprogress")
2. **Use helper functions** for save/read operations
3. **Follow the pattern** shown in `DatabaseExamples.h`

Example for a new boss donation system:
```cpp
// Save boss donation progress
void saveBossDonation(int bossIndex, int donationAmount) {
    if (!SaveToSystemDB("bossdonation", bossIndex, donationAmount)) {
        writeToFile("Error saving boss donation");
    }
}

// Read boss donation progress
int readBossDonation(int bossIndex) {
    return ReadFromSystemDB("bossdonation", bossIndex, 0);
}
```

## Migration from Old Systems

To migrate from INI-style files to the new database system:

1. **Read existing data** using `GetPrivateProfileIntA`
2. **Save to new database** using `SaveToSystemDB`
3. **Update read operations** to use `ReadFromSystemDB`

Example migration:
```cpp
// Read from old INI file
int oldValue = GetPrivateProfileIntA("Section", "Key", 0, "./old_file.txt");

// Save to new database
if (oldValue > 0) {
    SaveToSystemDB("newsystem", keyIndex, oldValue);
}
```

## File Structure

After implementation, your data files will be organized as:
```
./HethCFG/Data/
├── killtosummon.db      # Kill-to-summon progress
├── bossdonation.db      # Boss donation progress
├── playerstats.db       # Player statistics
├── questprogress.db     # Quest progress
└── [systemname].db      # Other systems
```

## Error Handling

The system includes comprehensive error handling:

- **Input validation**: Keys and values are range-checked
- **File security**: Prevents directory traversal attacks
- **Exception safety**: All operations are exception-safe
- **Logging**: Errors are logged using the existing `writeToFile` function

## Thread Safety

The database system is thread-safe:
- All operations use internal locking
- Multiple threads can safely access the same database
- No external synchronization required

## Performance Considerations

- **Auto-save**: Changes are automatically saved to disk
- **In-memory caching**: Data is cached in memory for fast access
- **Minimal overhead**: Lightweight implementation with small memory footprint

## Testing

A test program is provided in `test_database.cpp`:

```bash
# Compile the test (adjust paths as needed)
g++ -std=c++14 -I./Core test_database.cpp -o test_database

# Run the test
./test_database
```

## Files Included

- `Core/SimpleDatabase.h` - Main database implementation
- `Core/DatabaseExamples.h` - Usage examples for various systems
- `test_database.cpp` - Test program
- `SimpleDatabase_README.md` - This documentation

## Security Features

- **Input validation**: Prevents buffer overflows and invalid data
- **Path validation**: Prevents directory traversal attacks
- **Secure memory**: Uses SecureBuffer for sensitive operations
- **Bounds checking**: All array accesses are bounds-checked

## Compatibility

- **C++14 compatible**: Works with older compilers
- **Windows compatible**: Uses Windows-specific security functions
- **Existing codebase**: Integrates seamlessly with current security components
