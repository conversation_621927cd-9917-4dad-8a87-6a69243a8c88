int __fastcall GetFinalDamage(void *Target, void *edx, int Player, int Damage, int Argument)
{
	IChar IPlayer((void*)Player);
	IChar ITarget(Target);
	int CheckDamage = CChar::GetFinalDamage(Target,Player,Damage,Argument);

	//if(IPlayer.IsValid() && ITarget.IsValid() && IPlayer.GetType() == 0 && ITarget.GetType() == 0 && CChar::IsGState((int)Target,32)){
	//	IPlayer.Teleport(0, 257514, 259273);
	//	IPlayer.BoxMsg("Nice try, You cant attack fishers.");
	//	return 0;
	//}

	//if (IPlayer.IsValid() && ITarget.IsValid() && CheckDamage && IPlayer.GetType() == 0 && ITarget.GetType() == 0 && IPlayer.IsBuff(pvpAreaBuffId) && ITarget.IsBuff(pvpAreaBuffId)&& IPlayer.GetPartyID() == ITarget.GetPartyID() && IPlayer.IsParty() && ITarget.IsParty())
	//	return 0;
	
	//if (Protect32::Active == true && IPlayer.IsValid() && ITarget.IsValid() && CheckDamage && IPlayer.GetType() == 0 && ITarget.GetType() == 0 && IPlayer.IsBuff(BFBuffT1) && ITarget.IsBuff(BFBuffT1))
	//	return 0;

	//if (Protect32::Active == true && IPlayer.IsValid() && ITarget.IsValid() && CheckDamage && IPlayer.GetType() == 0 && ITarget.GetType() == 0 && IPlayer.IsBuff(BFBuffT2) && ITarget.IsBuff(BFBuffT2))
	//	return 0;

	if (IPlayer.IsValid() && ITarget.IsValid() && CheckDamage && IPlayer.GetType() == 0 && ITarget.GetType() == 1 && ITarget.GetMobIndex() == 318)
		QueenTopDmg[IPlayer.GetID()] += CheckDamage;


	return CheckDamage;
}