//void __fastcall PKKill(int * Player, void * __edx, int * Target) {
//  IChar IPlayer((void * ) Player);
//  IChar ITarget((void * ) Target);
//
//  if (IPlayer.IsOnline()) {
//
//    
//	//if (!CChar::IsEState((int) IPlayer.Offset, 16)) {
//
//      if (CChar::IsGState((int) IPlayer.Offset, 256)) {
//        if(PKKillCheck == 0)
//          return;
//        /*if (IPlayer.IsBuff(BFBuffT1) || IPlayer.IsBuff(BFBuffT2) || IPlayer.IsBuff(373) || IPlayer.IsBuff(374) || IPlayer.IsBuff(pvpAreaBuffId) || IPlayer.IsBuff(161) || IPlayer.IsBuff(160) || IPlayer.IsBuff(378) || IPlayer.IsBuff(170) || IPlayer.IsBuff(171) || IPlayer.IsBuff(162) || IPlayer.IsBuff(163) || IPlayer.IsBuff(373) || IPlayer.IsBuff(374))
//          return;*/
//
//
//        std::string Pname = (std::string) IPlayer.GetName();
//        std::string Tname = (std::string) ITarget.GetName();
//        std::string msg = Pname + " has assassinated " + Tname;
//        CPlayer::WriteAll(0xFF, "dsd", 247, msg.c_str(), 1);
//        return;
//      } else if(CChar::IsGState((int)ITarget.Offset, 256)){
//		     if(PKKillCheck == 0)
//		      return;
//
//        //if (IPlayer.IsBuff(BFBuffT1) || IPlayer.IsBuff(BFBuffT2) || IPlayer.IsBuff(373) || IPlayer.IsBuff(374) || IPlayer.IsBuff(pvpAreaBuffId) || IPlayer.IsBuff(161) || IPlayer.IsBuff(160) || IPlayer.IsBuff(378) || IPlayer.IsBuff(170) || IPlayer.IsBuff(171) || IPlayer.IsBuff(162) || IPlayer.IsBuff(163) || IPlayer.IsBuff(373) || IPlayer.IsBuff(374))
//        //  return;
//
//        std::string Pname = (std::string) IPlayer.GetName();
//        std::string Tname = (std::string) ITarget.GetName();
//        std::string msg = Pname + " has killed the assassin " + Tname;
//        CPlayer::WriteAll(0xFF, "dsd", 247, msg.c_str(), 7);
//        return;
//      }
//
//   // }
//    delete Player, Target;
//    CPlayer::PKKill(Player, Target);
//  }
//}