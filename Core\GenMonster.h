



#include "SecureBuffer.h"
#include "MemoryGuard.h"
#include <stdexcept>

// Constants for better maintainability
namespace GenMonsterConstants {
    constexpr int PROGRESS_SAVE_INTERVAL = 5;  // Save progress every 5%
    constexpr int BOSS_SPAWN_DURATION = 3600000;  // 1 hour in milliseconds
    constexpr int SUICIDE_BOMBER_INDEX = 9;
    constexpr int EXPLOSION_RANGE = 3;
    constexpr int MIN_DAMAGE = 1000;
    constexpr int MAX_DAMAGE = 1500;
}

// Helper function to check if progress should be saved
bool ShouldSaveProgress(int totalKilled, int minionAmount) {
    if (minionAmount <= 0) return false;

    for (int percent = GenMonsterConstants::PROGRESS_SAVE_INTERVAL; percent < 100;
         percent += GenMonsterConstants::PROGRESS_SAVE_INTERVAL) {
        if (totalKilled == static_cast<int>(minionAmount * percent / 100.0)) {
            return true;
        }
    }
    return false;
}

// Helper function to safely spawn bosses
void SafeSpawnBoss(int spawnMap, int spawnX, int spawnY, int bossIndex, int bossAmount) {
    try {
        // Validate spawn parameters
        if (spawnMap < 0 || bossIndex <= 0) {
            throw std::invalid_argument("Invalid spawn parameters");
        }

        int actualAmount = (bossAmount <= 1) ? 1 : bossAmount;
        for (int i = 0; i < actualAmount; i++) {
            Summon(0, spawnMap, spawnX, spawnY, bossIndex, 1, 0, 0,
                   GenMonsterConstants::BOSS_SPAWN_DURATION, 0);
        }
    }
    catch (const std::exception& e) {
        // Log error but don't crash the game
        // Could add logging here if available
    }
}

//real genmonster mobs die
int __fastcall GenMonsterDie(int Monster, void * edx, int Arg, int Arg1, int Arg2, int Arg3) {
    try {
        IChar IMonster((void*)Monster);

        // Validate monster object
        if (!IMonster.IsValid()) {
            return CMonsterGenMonster::Die(Monster, Arg, Arg1, Arg2, Arg3);
        }

        int mobIndex = IMonster.GetMobIndex();

        // Kill-to-summon system
        auto killToSummonIt = KillToSummon.find(mobIndex);
        if (killToSummonIt != KillToSummon.end() &&
            killToSummonIt->second.minionIndex == mobIndex) {

            // Cache the iterator to avoid repeated lookups
            auto& summonData = killToSummonIt->second;
            summonData.totalKilled++;

            int totalKilled = summonData.totalKilled;
            int minionAmount = summonData.minionAmount;

            // Validate amounts to prevent division by zero
            if (minionAmount <= 0) {
                return CMonsterGenMonster::Die(Monster, Arg, Arg1, Arg2, Arg3);
            }

            auto bossNameIt = KillToSummonBossName.find(mobIndex);
            if (bossNameIt == KillToSummonBossName.end()) {
                return CMonsterGenMonster::Die(Monster, Arg, Arg1, Arg2, Arg3);
            }

            std::string bossname = bossNameIt->second;

            // Save progress at intervals using helper function
            if (ShouldSaveProgress(totalKilled, minionAmount)) {
                saveKilled(summonData.minionIndex, summonData.totalKilled);
            }

            // Check if enough minions killed to spawn boss
            if (totalKilled >= minionAmount) {
                auto minionNameIt = KillToSummonMinionName.find(mobIndex);
                if (minionNameIt != KillToSummonMinionName.end()) {

                    // Create spawn message safely
                    SecureCharBuffer msgBuffer(512);  // Use secure buffer
                    std::string msg = "[" + bossname + "] has spawned after the death of " +
                                    Int2String(minionAmount) + "x [" + minionNameIt->second + "]";

                    if (msg.length() < 500) {  // Bounds check
                        CPlayer::WriteInMap(summonData.spawnMap, 0xFF, "dsd", 247, msg.c_str(), 5);
                        //DiscordLog(msg);

                        // Spawn boss safely
                        SafeSpawnBoss(summonData.spawnMap, summonData.spawnX, summonData.spawnY,
                                    summonData.bossIndex, summonData.bossAmount);
                    }
                }

                // Reset kill count
                summonData.totalKilled = 0;
                saveKilled(summonData.minionIndex, summonData.totalKilled);
            }
        }



        // Random player drop system
        auto rBDSIt = rBDSCheck.find(mobIndex);
        if (rBDSIt != rBDSCheck.end() && rBDSIt->second.rbossIndex == mobIndex) {

            // Cache the data to avoid repeated lookups
            const auto& rBDSData = rBDSIt->second;
            int Around = IMonster.GetObjectListAround(rBDSData.rdroprange);
            int playerAmountCount = 0;

            // Validate data
            if (rBDSData.playerAmount <= 0 || rBDSData.Items.empty()) {
                return CMonsterGenMonster::Die(Monster, Arg, Arg1, Arg2, Arg3);
            }

            while (Around && playerAmountCount < rBDSData.playerAmount) {
                IChar Object((void*)*(DWORD*)Around);

                if (Object.IsValid() && Object.GetType() == 0 &&
                    Object.GetLevel() >= rBDSData.rlevel &&
                    Object.GetMap() == IMonster.GetMap()) {

                    int overallRate = CTools::Rate(1, 1000);

                    if (overallRate > 0 && overallRate <= rBDSData.ovRate) {
                        int itemRate = CTools::Rate(1, 1000);

                        // Process items with bounds checking
                        for (size_t i = 0; i < rBDSData.Items.size() && i < rBDSData.Amounts.size() && i < rBDSData.Rates.size(); i++) {
                            int curRG = rBDSData.Items[i];
                            int curIA = rBDSData.Amounts[i];
                            int curIR = rBDSData.Rates[i];

                            // Validate item data
                            if (curRG <= 0 || curIA <= 0 || curIR <= 0) continue;

                            bool shouldGiveItem = false;
                            if (i == 0) {
                                shouldGiveItem = (itemRate > 0 && itemRate <= curIR);
                            } else {
                                int prevIR = rBDSData.Rates[i-1];
                                shouldGiveItem = (itemRate > prevIR && itemRate <= curIR);
                            }

                            if (shouldGiveItem) {
                                int itemFlags = (rBDSData.rbound == 0) ? 0 : 256;
                                CItem::InsertItem((int)Object.GetOffset(), 27, curRG, itemFlags, curIA, -1);
                                break;  // Only give one item per player
                            }
                        }

                        // Send message if configured (fix logic bug)
                        auto msgIt = rBDSCheckMsg.find(mobIndex);
                        if (msgIt != rBDSCheckMsg.end() &&
                            msgIt->second != "off" && msgIt->second != "0") {

                            SecureCharBuffer msgBuffer(256);
                            std::string playerName = Object.GetName();
                            std::string msg = playerName + " " + msgIt->second;

                            if (msg.length() < 250) {  // Bounds check
                                CPlayer::WriteAll(0xFF, "dsd", 247, msg.c_str(), 4);
                                //DiscordLog(msg);
                            }
                        }

                        playerAmountCount++;
                    }
                }

                Around = CBaseList::Pop((void*)Around);
            }
        }








  // drop sys
  if (BDSCheck.count(IMonster.GetMobIndex()) && BDSCheck.find(IMonster.GetMobIndex()) -> second.bossIndex == IMonster.GetMobIndex()) {
    int Around = IMonster.GetObjectListAround(BDSCheck.find(IMonster.GetMobIndex())->second.droprange);
    IChar Tanker((void * ) IMonster.GetMobTanker());
    std::string monsterName = BossName.find(IMonster.GetMobIndex())->second.c_str();
    std::string tankerName = Tanker.GetName();
    int shownotice = BDSCheck.find(IMonster.GetMobIndex())->second.shownotice;
    int levelMin = BDSCheck.find(IMonster.GetMobIndex())->second.level;
    int isBound = BDSCheck.find(IMonster.GetMobIndex())->second.bound;
    int exp = BDSCheck.find(IMonster.GetMobIndex())->second.exp;
    int money = BDSCheck.find(IMonster.GetMobIndex())->second.money;

	int GetSize = BDSCheck.find(IMonster.GetMobIndex())->second.Items.size();
	

    if (shownotice == 1) {

      std::string msg1 = "Thank you for saving the world from " + monsterName;
      std::string msg2 = "Rewards has been given for level " + Int2String(levelMin) + "+ warriors.";
      std::string msg3 = "[ " + tankerName + " ] MVP !!";
      CPlayer::WriteAll(0xFF, "dsd", 247, msg1.c_str(), 4);
      CPlayer::WriteAll(0xFF, "dsd", 247, msg2.c_str(), 4);
      CPlayer::WriteAll(0xFF, "dsd", 247, msg3.c_str(), 4);
    }


    while (Around) {
      IChar Object((void * ) * (DWORD * ) Around);
      if (Object.GetType() == 0 && Object.GetLevel() >= levelMin && Object.GetMap() == IMonster.GetMap()) {
        //give exp
        if (exp > 1)
          ( * (int(__cdecl ** )(int, signed int, signed int, unsigned __int64, unsigned __int64))( * (DWORD * ) Object.GetOffset() + 88))((int) Object.GetOffset(), 25, 1, (unsigned __int64) exp, HIDWORD(exp));
		//give money
		if (money != 0)
            CItem::InsertItem((int) Object.GetOffset(), 27, 31, 0, money, -1);

        // give drops
		int Rate = CTools::Rate(1,1000);
		if(BDSCheck.find(IMonster.GetMobIndex())->second.rngdrops == 1){
			
		bool gotItem = false;
		for (int i = 0; i < GetSize; i++)
			{
				int curRG = BDSCheck.find(IMonster.GetMobIndex())->second.Items[i];
				int curIA = BDSCheck.find(IMonster.GetMobIndex())->second.Amounts[i];
				int curIR = BDSCheck.find(IMonster.GetMobIndex())->second.Rates[i];
				
				if(i == 0){
					if(Rate > 0 && Rate <= curIR && curRG != 0 && curIA != 0){
						gotItem = true;

					if(isBound == 0)
						CItem::InsertItem((int)Object.GetOffset(),27,curRG,0,curIA,-1);
					else
						CItem::InsertItem((int)Object.GetOffset(),27,curRG,256,curIA,-1);

					
				}
				}else{
					int prevIR = BDSCheck.find(IMonster.GetMobIndex())->second.Rates[i-1];

					if(Rate > prevIR && Rate <= curIR && curRG != 0 && curIA != 0){
						gotItem = true;

						if(isBound == 0)
							CItem::InsertItem((int)Object.GetOffset(),27,curRG,0,curIA,-1);
						else
							CItem::InsertItem((int)Object.GetOffset(),27,curRG,256,curIA,-1);
						

					}
				
				}


			}

				if(gotItem == false){
					Object.SystemMessage("Sorry you are unlucky and got no drops, good luck next time.", TEXTCOLOR_RED);
				}
		}else if(BDSCheck.find(IMonster.GetMobIndex())->second.rngdrops == 0){

				for (int i = 0; i < GetSize; i++)
				{
					int curRG = BDSCheck.find(IMonster.GetMobIndex())->second.Items[i];
					int curIA = BDSCheck.find(IMonster.GetMobIndex())->second.Amounts[i];

					if(curRG != 0 && curIA != 0 && isBound == 0)
						CItem::InsertItem((int)Object.GetOffset(),27,curRG,0,curIA,-1);

					if(curRG != 0 && curIA != 0 && isBound == 1)
						CItem::InsertItem((int)Object.GetOffset(),27,curRG,256,curIA,-1);
				
	

				}


			}
      }
      Around = CBaseList::Pop((void * ) Around);
    }
  }



	//monster explosion system
	// if(IMonster.GetMobIndex() == 9){

	// 	IMonster.AddFxToTarget("davi_ef131_05", 1, 0, 0); // effect
	//   int Around = IMonster.GetObjectListAround(3); // range
    //   while (Around) {
    //     IChar Object((void * ) * (DWORD * ) Around);
    //     if (Object.GetType() == 0){
	// 	IMonster.OktayDamageArea(Object,CTools::Rate(1000, 1500),1);
	// 	//Object.SystemMessage("Suicide Bomber has exploaded on you.", TEXTCOLOR_RED);
	// 	}
    //     Around = CBaseList::Pop((void * ) Around);
    //   }
	// IMonster.MobDelete();
	// }





        return CMonsterGenMonster::Die(Monster, Arg, Arg1, Arg2, Arg3);
    }
    catch (const std::exception& e) {
        // Log error but don't crash the game
        return CMonsterGenMonster::Die(Monster, Arg, Arg1, Arg2, Arg3);
    }
}



int __fastcall GenMonsterTick(void * Monster, void * edx) {
    try {
        IChar IMonster(Monster);

        // Validate monster object
        if (!IMonster.IsValid()) {
            return CMonsterReal::Tick(Monster);
        }

        int mobIndex = IMonster.GetMobIndex();
        DWORD currentTick = GetTickCount();

        // Monster skills system
        // auto skillIt = MonsterSkillCheak.find(mobIndex);
        // if (skillIt != MonsterSkillCheak.end() &&
        //     skillIt->second.monsterSkillIndex == mobIndex &&
        //     currentTick > skillIt->second.deskilldelay) {

        //     // Cache the skill data to avoid repeated lookups
        //     auto& skillData = skillIt->second;

        //     // Calculate HP thresholds safely
        //     unsigned __int64 maxHp = IMonster.GetMaxHp();
        //     if (maxHp > 0) {
        //         int hpPercMin = static_cast<int>(maxHp * skillData.skillhpmin / 100);
        //         int hpPercMax = static_cast<int>(maxHp * skillData.skillhpmax / 100);

        //         if (inRange(hpPercMin, hpPercMax, IMonster.GetCurHp())) {
        //             // Update skill delay
        //             skillData.deskilldelay = currentTick + skillData.mskilldelay;

        //             // Validate damage values
        //             int damageMin = (skillData.skilldamagemin > 1) ? skillData.skilldamagemin : 1;
        //             int damageMax = (skillData.skilldamagemax > damageMin) ? skillData.skilldamagemax : damageMin;
        //             int skillRange = (skillData.skillrange > 1) ? skillData.skillrange : 1;

        //             // Handle animation if configured
        //             if (skillData.animationID != 0) {
        //                 IChar Tanker((void*)IMonster.GetMobTanker());
        //                 if (Tanker.IsValid()) {
        //                     IMonster.UnAttack(skillData.animationDelay);
        //                     int damage = CTools::Rate(damageMin, damageMax);
        //                     IMonster.OktayDamageSingle(Tanker, damage, skillData.animationID);
        //                 }
        //             }

        //             // Handle visual effects (fix logic bug)
        //             auto fxIt = MonsterSkillFx.find(mobIndex);
        //             if (fxIt != MonsterSkillFx.end() &&
        //                 fxIt->second != " " && fxIt->second != "off") {
        //                 IMonster.AddFxToTarget(fxIt->second.c_str(), 1, 0, 0);
        //             }

        //             // Handle AoE damage
        //             if (skillData.AoE == 1) {
        //                 int Around = IMonster.GetObjectListAround(skillRange);
        //                 while (Around) {
        //                     IChar Object((void*)*(DWORD*)Around);
        //                     if (Object.IsValid() && Object.GetType() == 0) {
        //                         int damage = CTools::Rate(damageMin, damageMax);
        //                         IMonster.OktayDamageStorm(Object, damage);
        //                     }
        //                     Around = CBaseList::Pop((void*)Around);
        //                 }
        //             }
        //         }
        //     }
        // }

        // Monster summon system
        auto summonIt = MonsterSummonCheak.find(mobIndex);
        if (summonIt != MonsterSummonCheak.end() &&
            summonIt->second.monsterSummonIndex == mobIndex &&
            currentTick > summonIt->second.desmnskilldelay) {

            // Cache the summon data to avoid repeated lookups
            auto& summonData = summonIt->second;

            // Calculate HP thresholds safely
            unsigned __int64 maxHp = IMonster.GetMaxHp();
            if (maxHp > 0) {
                unsigned __int64 hpPercMin = maxHp * summonData.summonhpmin / 100;
                unsigned __int64 hpPercMax = maxHp * summonData.summonhpmax / 100;

                if (inRange(hpPercMin, hpPercMax, IMonster.GetCurHp())) {
                    // Update summon delay
                    summonData.desmnskilldelay = currentTick + summonData.smnskilldelay;

                    // Validate summon parameters
                    if (summonData.summonMinionIndex > 0 && summonData.summonMinionAmount > 0) {
                        int curMap = IMonster.GetMap();
                        int curX = IMonster.GetX();
                        int curY = IMonster.GetY();
                        int summonRange = (summonData.summonrange > 0) ? summonData.summonrange : 1;

                        // Handle animation if configured
                        if (summonData.animationID != 0) {
                            IChar Tanker((void*)IMonster.GetMobTanker());
                            if (Tanker.IsValid()) {
                                IMonster.UnAttack(summonData.animationDelay);
                                IMonster.OktayDamageSingle(Tanker, 500, summonData.animationID);
                            }
                        }

                        // Handle visual effects (fix logic bug)
                        auto summonFxIt = MonsterSummonFx.find(mobIndex);
                        if (summonFxIt != MonsterSummonFx.end() &&
                            summonFxIt->second != " " && summonFxIt->second != "off") {
                            IMonster.AddFxToTarget(summonFxIt->second.c_str(), 1, 0, 0);
                        }

                        // Summon minions with bounds checking
                        int maxSummons = (summonData.summonMinionAmount > 20) ? 20 : summonData.summonMinionAmount;
                        for (int i = 0; i < maxSummons; i++) {
                            int spawnX = CTools::Rate(curX, curX + summonRange);
                            int spawnY = CTools::Rate(curY, curY + summonRange);
                            Summon(0, curMap, spawnX, spawnY, summonData.summonMinionIndex, 1, 1, 0,
                                   GenMonsterConstants::BOSS_SPAWN_DURATION, 0);
                        }
                    }
                }
            }
        }

        // Monster debuff system
        // auto buffIt = MonsterBuffCheak.find(mobIndex);
        // if (buffIt != MonsterBuffCheak.end() &&
        //     buffIt->second.monsterIndex == mobIndex &&
        //     currentTick > buffIt->second.debuffdelay) {

        //     // Cache the buff data to avoid repeated lookups
        //     auto& buffData = buffIt->second;

        //     // Calculate HP threshold safely
        //     unsigned __int64 maxHp = IMonster.GetMaxHp();
        //     if (maxHp > 0) {
        //         int hpThreshold = static_cast<int>(maxHp * buffData.debuffhp / 100);

        //         if (IMonster.GetCurHp() <= hpThreshold) {
        //             // Update debuff delay
        //             buffData.debuffdelay = currentTick + buffData.skilldelay;

        //             // Validate debuff parameters
        //             if (buffData.debuffid > 0 && buffData.debuffcd > 0 && buffData.debuffrange > 0) {

        //                 // Handle animation if configured
        //                 if (buffData.animationID != 0) {
        //                     IChar Tanker((void*)IMonster.GetMobTanker());
        //                     if (Tanker.IsValid()) {
        //                         IMonster.UnAttack(buffData.animationDelay);
        //                         IMonster.OktayDamageSingle(Tanker, 500, buffData.animationID);
        //                     }
        //                 }

        //                 // Handle visual effects (fix logic bug)
        //                 auto buffFxIt = MonsterBuffFx.find(mobIndex);
        //                 if (buffFxIt != MonsterBuffFx.end() &&
        //                     buffFxIt->second != " " && buffFxIt->second != "off") {
        //                     IMonster.AddFxToTarget(buffFxIt->second.c_str(), 1, 0, 0);
        //                 }

        //                 // Apply debuffs to nearby players
        //                 int Around = IMonster.GetObjectListAround(buffData.debuffrange);
        //                 while (Around) {
        //                     IChar Object((void*)*(DWORD*)Around);

        //                     if (Object.IsValid() && Object.GetType() == 0 &&
        //                         Object.GetClass() != buffData.excludedclass) {
        //                         Object.Buff(buffData.debuffid, buffData.debuffcd, buffData.debuffval);
        //                     }

        //                     Around = CBaseList::Pop((void*)Around);
        //                 }
        //             }
        //         }
        //     }
        // }

        return CMonsterReal::Tick(Monster);
    }
    catch (const std::exception& e) {
        // Log error but don't crash the game
        return CMonsterReal::Tick(Monster);
    }
}