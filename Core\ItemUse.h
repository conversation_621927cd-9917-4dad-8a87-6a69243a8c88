int __fastcall ItemUse(void *ItemOffset, void *edx, int PlayerOffset)
{
	IItem Item(ItemOffset);
	IChar IPlayer((void*)PlayerOffset);







	//inventory expansion
	//if (IPlayer.IsOnline() && ItemInvExCheak.count(Item.CheckIndex()) && ItemInvExCheak.find(Item.CheckIndex())->second.iteminvindex == Item.CheckIndex())
	//{

	//				int invCD = ItemInvExCheak.find(Item.CheckIndex())->second.invexcd;
	//			

 //                   if (IPlayer.IsBuff(172) && IPlayer.IsBuff(173) && IPlayer.IsBuff(174))
 //                   {
 //                       IPlayer.SystemMessage("You cannot add more the inventory slot(s).",
 //                           TEXTCOLOR_RED);
 //                      return Item.GetAmount();
 //                   }else if (!IPlayer.IsBuff(172) && !IPlayer.IsBuff(173) && !IPlayer.IsBuff(174))
 //                   {
 //                       IPlayer.Buff(174, invCD, 0);
 //                       CPlayer::Write(IPlayer.GetOffset(), 204, "d", 36);
 //                       CPlayer::Write(IPlayer.GetOffset(), 181, "dwd", IPlayer.GetID(), 499, invCD);
	//					(*(int (__thiscall **)(DWORD, void *, signed int, signed int))(*(DWORD*)ItemOffset + 120))((int)ItemOffset,IPlayer.GetOffset(),9,-1);
 //                      return Item.GetAmount();
 //                   }else if (!IPlayer.IsBuff(172) && !IPlayer.IsBuff(173) && IPlayer.IsBuff(174))
 //                   {
 //                       IPlayer.Buff(173, invCD, 0);
 //                       CPlayer::Write(IPlayer.GetOffset(), 204, "d", 72);
 //                       CPlayer::Write(IPlayer.GetOffset(), 181, "dwd", IPlayer.GetID(), 500, invCD);
	//					(*(int (__thiscall **)(DWORD, void *, signed int, signed int))(*(DWORD*)ItemOffset + 120))((int)ItemOffset,IPlayer.GetOffset(),9,-1);
 //                      return Item.GetAmount();
 //                   }else if (!IPlayer.IsBuff(172) && IPlayer.IsBuff(173) && IPlayer.IsBuff(174))
 //                   {
 //                       IPlayer.Buff(172, invCD, 0);
 //                       CPlayer::Write(IPlayer.GetOffset(), 204, "d", 108);
 //                       CPlayer::Write(IPlayer.GetOffset(), 181, "dwd", IPlayer.GetID(), 501, invCD);
	//					(*(int (__thiscall **)(DWORD, void *, signed int, signed int))(*(DWORD*)ItemOffset + 120))((int)ItemOffset,IPlayer.GetOffset(),9,-1);
 //                      return Item.GetAmount();
 //                   }


	//}


	//block item
	//if(IPlayer.IsOnline() && ItemBlock.count(Item.CheckIndex()) && ItemBlock.find(Item.CheckIndex())->second.index == Item.CheckIndex() && ItemBlock.find(Item.CheckIndex())->second.disabled == 1){
	//	IPlayer.BoxMsg("This item has been disabled by the admins");
	//	return Item.GetAmount();
	//}

	//// use item get buff system
	//	if (IPlayer.IsOnline() && ItemBuffCheak.count(Item.CheckIndex()) && ItemBuffCheak.find(Item.CheckIndex())->second.itemIndex == Item.CheckIndex())
	//{
	//	int buffid = ItemBuffCheak.find(Item.CheckIndex())->second.buffid;
	//	int buffcd = ItemBuffCheak.find(Item.CheckIndex())->second.buffcd;
	//	int buffval = ItemBuffCheak.find(Item.CheckIndex())->second.buffval;
	//	if(!IPlayer.IsBuff(buffid)){
	//	IPlayer.Buff(buffid,buffcd,buffval);
	//	(*(int (__thiscall **)(DWORD, void *, signed int, signed int))(*(DWORD*)ItemOffset + 120))((int)ItemOffset,IPlayer.GetOffset(),9,-1);
	//	return Item.GetAmount();
	//	}
	//	IPlayer.SystemMessage("This buff is already active.",TEXTCOLOR_RED);
	//	return Item.GetAmount();
	//}

	////item use to open html
	//	if (IPlayer.IsOnline() && openHtml.count(Item.CheckIndex()) && openHtml.find(Item.CheckIndex())->second.Index == Item.CheckIndex())
	//{
	//	IPlayer.OpenHTML(openHtml.find(Item.CheckIndex())->second.Html);
	//	return Item.GetAmount();
	//}

		// boxes
	if (IPlayer.IsOnline() && BoxesCheck.count(Item.CheckIndex()) && BoxesCheck.find(Item.CheckIndex())->second.BoxIndex == Item.CheckIndex())
		{

		
	int isBound = BoxesCheck.find(Item.CheckIndex())->second.BoxIsBound;
	int GetSize = BoxesCheck.find(Item.CheckIndex())->second.Items.size();


		for (int i = 0; i < GetSize; i++)
		{
			int curRG = BoxesCheck.find(Item.CheckIndex())->second.Items[i];
			int curIA = BoxesCheck.find(Item.CheckIndex())->second.Amounts[i];

			if(curRG != 0 && curIA != 0 && isBound == 0)
				CItem::InsertItem((int)IPlayer.GetOffset(),27,curRG,0,curIA,-1);

			if(curRG != 0 && curIA != 0 && isBound == 1)
				CItem::InsertItem((int)IPlayer.GetOffset(),27,curRG,256,curIA,-1);
			

		}

		(*(int (__thiscall **)(DWORD, void *, signed int, signed int))(*(DWORD*)ItemOffset + 120))((int)ItemOffset,IPlayer.GetOffset(),9,-1);
		return Item.GetAmount();
		}


	//randomboxes
		if (IPlayer.IsOnline() && RandomBoxesCheck.count(Item.CheckIndex()) && RandomBoxesCheck.find(Item.CheckIndex())->second.RandomBoxIndex == Item.CheckIndex())
	{
		int isBound = RandomBoxesCheck.find(Item.CheckIndex())->second.RandomBoxIsBound;
		int GetSize = RandomBoxesCheck.find(Item.CheckIndex())->second.Items.size();
		int Rate = CTools::Rate(0,GetSize-1);


			for (int i = 0; i < GetSize; i++)
			{
				if(Rate == i){
				int curRG = RandomBoxesCheck.find(Item.CheckIndex())->second.Items[i];
				int curIA = RandomBoxesCheck.find(Item.CheckIndex())->second.Amounts[i];

				if(curRG != 0 && curIA != 0 && isBound == 0)
					CItem::InsertItem((int)IPlayer.GetOffset(),27,curRG,0,curIA,-1);
				if(curRG != 0 && curIA != 0 && isBound == 1)
					CItem::InsertItem((int)IPlayer.GetOffset(),27,curRG,256,curIA,-1);
				}

			}

		(*(int (__thiscall **)(DWORD, void *, signed int, signed int))(*(DWORD*)ItemOffset + 120))((int)ItemOffset,IPlayer.GetOffset(),9,-1);
		return Item.GetAmount();
		}




	// rngboxes
if (IPlayer.IsOnline() && RNGBoxesCheck.count(Item.CheckIndex()) && RNGBoxesCheck.find(Item.CheckIndex())->second.RNGBoxIndex == Item.CheckIndex())
	{
		int isBound = RNGBoxesCheck.find(Item.CheckIndex())->second.RNGBoxIsBound;
		int GetSize = RNGBoxesCheck.find(Item.CheckIndex())->second.Items.size();
		int Rate = CTools::Rate(1,1000);
		bool gotItem = false;

		for (int i = 0; i < GetSize; i++)
			{
				int curRG = RNGBoxesCheck.find(Item.CheckIndex())->second.Items[i];
				int curIA = RNGBoxesCheck.find(Item.CheckIndex())->second.Amounts[i];
				int curIR = RNGBoxesCheck.find(Item.CheckIndex())->second.Rates[i];
				
				if(i == 0){
					if(Rate > 0 && Rate <= curIR && curRG != 0 && curIA != 0){

					if(isBound == 0)
						CItem::InsertItem((int)IPlayer.GetOffset(),27,curRG,0,curIA,-1);
					if(isBound == 1)
						CItem::InsertItem((int)IPlayer.GetOffset(),27,curRG,256,curIA,-1);


						if(RNGBoxesCheck.find(Item.CheckIndex())->second.donotice == 1){
					std::string name = IPlayer.GetName();
					std::string itemname = RNGBoxesName.find(Item.CheckIndex())->second.c_str();
					std::string msg =  name + " " + itemname;
					CPlayer::WriteAll(0xFF, "dsd", 247, msg , 4);
						}


					gotItem = true;
				}
				}else{
					int prevIR = RNGBoxesCheck.find(Item.CheckIndex())->second.Rates[i-1];

					if(Rate > prevIR && Rate <= curIR && curRG != 0 && curIA != 0){
						gotItem = true;

						if(isBound == 0)
							CItem::InsertItem((int)IPlayer.GetOffset(),27,curRG,0,curIA,-1);
						if(isBound == 1)
							CItem::InsertItem((int)IPlayer.GetOffset(),27,curRG,256,curIA,-1);
						

					}
				
				}


			}

				if(gotItem == false){
					IPlayer.SystemMessage("Sorry you are unlucky and got no Item, good luck next time.", TEXTCOLOR_RED);
				}



				(*(int (__thiscall **)(DWORD, void *, signed int, signed int))(*(DWORD*)ItemOffset + 120))((int)ItemOffset,IPlayer.GetOffset(),9,-1);
		return Item.GetAmount();
	}





	// give all buff scrolls item
	//if (IPlayer.IsOnline() && Item.CheckIndex() == 333 && !IPlayer.IsBuff(51) && !IPlayer.IsBuff(61))
	//{
	//	IPlayer.Buff(51,7200,0);
	//	IPlayer.Buff(52,7200,0);
	//	IPlayer.Buff(54,7200,0);
	//	IPlayer.Buff(55,7200,0);
	//	IPlayer.Buff(56,7200,0);
	//	IPlayer.Buff(57,7200,0);
	//	IPlayer.Buff(60,7200,5);
	//	IPlayer.Buff(61,7200,10);
	//	IPlayer.Buff(62,7200,5);
	//	IPlayer.Buff(63,7200,10);
	//	IPlayer.Buff(64,7200,5);
	//	IPlayer.SetBuffIcon(7200*1000, 0, 919, 60);
	//	IPlayer.SetBuffIcon(7200*1000, 0, 920, 61);
	//	IPlayer.SetBuffIcon(7200*1000, 0, 921, 62);
	//	IPlayer.SetBuffIcon(7200*1000, 0, 922, 63);
	//	IPlayer.SetBuffIcon(7200*1000, 0, 923, 64);
	//	IPlayer.SetBuffIcon(7200*1000, 0, 924, 65);
	//	IPlayer.SetBuffIcon(7200*1000, 0, 926, 67);
	//	IPlayer.SetBuffIcon(7200*1000, 0, 927, 68);
	//	IPlayer.SetBuffIcon(7200*1000, 0, 928, 69);
	//	IPlayer.SetBuffIcon(7200*1000, 0, 929, 70);
	//	IPlayer.SetBuffIcon(7200*1000, 0, 930, 71);



	//	(*(int (__thiscall **)(DWORD, void *, signed int, signed int))(*(DWORD*)ItemOffset + 120))((int)ItemOffset,IPlayer.GetOffset(),9,-1);
	//	return Item.GetAmount();
	//}

	//// give all meals item
	//if (IPlayer.IsOnline() && Item.CheckIndex() == 555 && !IPlayer.IsBuff(175) && !IPlayer.IsBuff(176) && !IPlayer.IsBuff(177) && !IPlayer.IsBuff(178)&& !IPlayer.IsBuff(179) && !IPlayer.IsBuff(200) && !IPlayer.IsBuff(201) && !IPlayer.IsBuff(202) && !IPlayer.IsBuff(203) && !IPlayer.IsBuff(204))
	//{
	//	

	//	IPlayer.Buff(175,3670,0);
 //       //IPlayer.Buff(272,3600,0);
 //       //IPlayer.Buff(261,3600,0);
	//	IPlayer.Buff(200,3750,0);
 //       IPlayer.SetBuffIcon(3750000,0,3645,430);
 //       IPlayer.IncreaseMaxHp(1450);


	//	IPlayer.Buff(176,3670,0);
 //      // IPlayer.Buff(264,3600,0);
 //      // IPlayer.Buff(265,3600,0);
	//   IPlayer.Buff(201,3750,0);
 //       IPlayer.SetBuffIcon(3750000,0,3601,415);
 //       IPlayer.IncreaseMaxHp(500);
 //       IPlayer.IncreaseMaxMp(500);


	//	IPlayer.Buff(177,3670,0);
 //       //IPlayer.Buff(266,3600,0);
 //       //IPlayer.Buff(267,3600,0);
	//	IPlayer.Buff(202,3750,0);
 //       IPlayer.SetBuffIcon(3750000,0,3604,418);
 //       IPlayer.AddMaxAttack(75);
 //       IPlayer.AddMinAttack(50);
 //       IPlayer.AddEva(10);

	//	IPlayer.Buff(178,3670,0);
 //       //IPlayer.Buff(268,3600,0);
 //      // IPlayer.Buff(269,3600,0);
	//   IPlayer.Buff(203,3750,0);
 //       IPlayer.SetBuffIcon(3750000,0,3603,417);
 //       IPlayer.AddMaxAttack(100);


	//	IPlayer.Buff(179,3670,0);
 //      // IPlayer.Buff(270,3600,0);
 //      // IPlayer.Buff(271,3600,0);
	//   IPlayer.Buff(204,3750,0);
 //       IPlayer.SetBuffIcon(3750000,0,3602,416);
 //       IPlayer.AddMinAttack(75);
 //       IPlayer.AddOTP(10);
	//	(*(int (__thiscall **)(DWORD, void *, signed int, signed int))(*(DWORD*)ItemOffset + 120))((int)ItemOffset,IPlayer.GetOffset(),9,-1);
	//	return Item.GetAmount();
	//}


	
	return CItemGeneral::Use(ItemOffset, PlayerOffset);
}