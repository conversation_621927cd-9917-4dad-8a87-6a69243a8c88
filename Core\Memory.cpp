#include <Windows.h>
#include "Memory.h"
#pragma comment(lib, "Detours/detours.lib")
#include "Interface.h"
#include "Detours/Detours.h"
#include "SecureBuffer.h"
#include "MemoryGuard.h"
#include <stdexcept>
#include <memory>

CREATE_INTERFACE(IMemory)

void IMemory::Copy(void* Destination, void* Source, size_t Size)
{
	// Input validation
	if (!Destination || !Source || Size == 0) {
		throw std::invalid_argument("IMemory::Copy: Invalid parameters");
	}

	unsigned long p1, p2;

	// Use RAII for automatic protection restoration
	auto restore_dest_protection = [Destination, Size, &p1]() {
		VirtualProtect(Destination, Size, p1, &p1);
		};

	auto restore_source_protection = [Source, Size, &p2]() {
		VirtualProtect(Source, Size, p2, &p2);
		};

	// Protect destination memory
	if (!VirtualProtect(Destination, Size, PAGE_EXECUTE_READWRITE, &p1)) {
		throw std::runtime_error("IMemory::Copy: Failed to protect destination memory");
	}

	// Use RAII guard to ensure destination protection is restored
	ScopeGuard dest_guard(restore_dest_protection);

	// Protect source memory
	if (!VirtualProtect(Source, Size, PAGE_EXECUTE_READWRITE, &p2)) {
		throw std::runtime_error("IMemory::Copy: Failed to protect source memory");
	}

	// Use RAII guard to ensure source protection is restored
	ScopeGuard source_guard(restore_source_protection);

	// Perform the copy operation
	CopyMemory(Destination, Source, Size);

	// Protection is automatically restored by RAII guards when function exits
}

void IMemory::Fill(void* Destination, unsigned char Fill, size_t Size, bool Recoverable)
{
	// Input validation
	if (!Destination || Size == 0) {
		throw std::invalid_argument("IMemory::Fill: Invalid parameters");
	}

	try {
		// Use RAII buffer instead of manual new/delete
		SecureBuffer<unsigned char> data(Size, Fill);

		// Create patch with exception safety
		std::unique_ptr<Patch> patch;
		try {
			patch.reset(new Patch(Destination, data.data(), Size, Recoverable));
		}
		catch (...) {
			// If patch creation fails, data buffer is automatically cleaned up
			throw;
		}

		// Store the patch - only after successful creation
		this->m_Patches[Destination] = patch.release();

		// SecureBuffer automatically cleans up when function exits
	}
	catch (const std::bad_alloc&) {
		throw std::runtime_error("IMemory::Fill: Memory allocation failed");
	}
}

void IMemory::Fill(unsigned long Destination, unsigned char Fill, size_t Size, bool Recoverable)
{
	this->Fill((void*)Destination, Fill, Size, Recoverable);
}

void IMemory::Set(void* Destination, const char* Data, size_t Size, bool Recoverable)
{
	if (!Destination || !Data || Size == 0) {
		throw std::invalid_argument("IMemory::Set: Invalid parameters");
	}

	try {
		std::unique_ptr<Patch> patch;
		try {
			patch.reset(new Patch(Destination, (unsigned char*)const_cast<char*>(Data), Size, Recoverable));
		}
		catch (...) {
			throw;
		}

		this->m_Patches[Destination] = patch.release();
	}
	catch (const std::bad_alloc&) {
		throw std::runtime_error("IMemory::Set: Memory allocation failed");
	}
}

void IMemory::Set(unsigned long Destination, const char* Data, size_t Size, bool Recoverable)
{
	this->Set((void*)Destination, Data, Size, Recoverable);
}

void IMemory::Set(void* Destination, unsigned char* Data, size_t Size, bool Recoverable)
{
	this->Set(Destination, (const char*)Data, Size, Recoverable);
}

void IMemory::Set(unsigned long Destination, unsigned char* Data, size_t Size, bool Recoverable)
{
	this->Set((void*)Destination, Data, Size, Recoverable);
}

void IMemory::Hook(void* Address, void* Destination, unsigned char Instruction, size_t Size, bool Recoverable)
{
	// Input validation
	if (!Address || !Destination || Size < 5) {
		throw std::invalid_argument("IMemory::Hook: Invalid parameters (minimum size is 5 bytes for hook)");
	}

	try {
		// Use RAII buffer instead of manual new/delete
		SecureBuffer<unsigned char> data(Size, static_cast<unsigned char>(IMemory::_I_NOP));

		// Calculate target address with overflow checking
		unsigned long addr_val = reinterpret_cast<unsigned long>(Address);
		unsigned long dest_val = reinterpret_cast<unsigned long>(Destination);

		// Check for potential overflow in address calculation
		if (dest_val < addr_val + 5) {
			// Handle negative offset case
			unsigned long target = dest_val - (addr_val + 5);
			data[0] = Instruction;

			// Safe memory copy with bounds checking
			if (Size >= 5) {
				data.safe_copy_from(reinterpret_cast<const unsigned char*>(&target), 4, 1);
			}
		}
		else {
			unsigned long target = dest_val - (addr_val + 5);
			data[0] = Instruction;

			// Safe memory copy with bounds checking
			if (Size >= 5) {
				data.safe_copy_from(reinterpret_cast<const unsigned char*>(&target), 4, 1);
			}
		}

		// Create patch with exception safety
		std::unique_ptr<Patch> patch;
		try {
			patch.reset(new Patch(Address, data.data(), Size, Recoverable));
		}
		catch (...) {
			// If patch creation fails, data buffer is automatically cleaned up
			throw;
		}

		// Store the patch - only after successful creation
		this->m_Patches[Address] = patch.release();

		// SecureBuffer automatically cleans up when function exits
	}
	catch (const std::bad_alloc&) {
		throw std::runtime_error("IMemory::Hook: Memory allocation failed");
	}
	catch (const BufferOverflowException& e) {
		throw std::runtime_error(std::string("IMemory::Hook: Buffer overflow - ") + e.what());
	}
}

void IMemory::Hook(unsigned long Address, void* Destination, unsigned char Instruction, size_t Size, bool Recoverable)
{
	this->Hook((void*)Address, Destination, Instruction, Size, Recoverable);
}

void IMemory::Hook(unsigned long Address[], size_t Count, void* Destination, unsigned char Instruction, size_t Size, bool Recoverable)
{
	for (size_t i = 0; i < Count; i++)
		this->Hook((void*)Address[i], Destination, Instruction, Size, Recoverable);
}

void IMemory::Hook(void* Address, unsigned long Destination, unsigned char Instruction, size_t Size, bool Recoverable)
{
	this->Hook(Address, (void*)Destination, Instruction, Size, Recoverable);
}

void IMemory::Hook(unsigned long Address, unsigned long Destination, unsigned char Instruction, size_t Size, bool Recoverable)
{
	this->Hook((void*)Address, (void*)Destination, Instruction, Size, Recoverable);
}

void IMemory::Hook(unsigned long Address[], size_t Count, unsigned long Destination, unsigned char Instruction, size_t Size, bool Recoverable)
{
	for (size_t i = 0; i < Count; i++)
		this->Hook((void*)Address[i], (void*)Destination, Instruction, Size, Recoverable);
}

void IMemory::Restore(void* Address)
{
	// Input validation
	if (!Address) {
		return; // Nothing to restore for null address
	}

	try {
		auto it = this->m_Patches.find(Address);
		if (it != this->m_Patches.end()) {
			// Use RAII to ensure cleanup even if exception occurs
			std::unique_ptr<Patch> patch_guard(it->second);

			// Remove from map first to maintain consistency
			this->m_Patches.erase(it);

			// patch_guard automatically deletes the patch when it goes out of scope
		}
	}
	catch (const std::exception& e) {
		// Log error but don't propagate - restore operations should be robust
		// In a real implementation, you might want to log this error
		// For now, we'll silently handle it to maintain compatibility
	}
}

void IMemory::Restore(unsigned long Address)
{
	this->Restore((void*)Address);
}

void IMemory::Restore(unsigned long Address[], size_t Count)
{
	// Input validation
	if (!Address || Count == 0) {
		return;
	}

	// Restore each address safely
	for (size_t i = 0; i < Count; i++) {
		try {
			this->Restore((void*)Address[i]); // Fixed: was Address instead of Address[i]
		}
		catch (...) {
			// Continue with other addresses even if one fails
			// This ensures partial restoration doesn't break everything
		}
	}
}

void IMemory::Restore(unsigned long Address, void* Destination)
{
	// Input validation
	if (!Address || !Destination) {
		throw std::invalid_argument("IMemory::Restore: Invalid parameters");
	}

	// Use RAII for transaction management
	auto cleanup_transaction = []() {
		DetourTransactionAbort(); // Abort transaction on failure
		};

	if (DetourTransactionBegin() != NO_ERROR) {
		throw std::runtime_error("IMemory::Restore: Failed to begin detour transaction");
	}

	// Use RAII guard to ensure transaction is properly handled
	ScopeGuard transaction_guard(cleanup_transaction);

	if (DetourUpdateThread(GetCurrentThread()) != NO_ERROR) {
		throw std::runtime_error("IMemory::Restore: Failed to update thread");
	}

	if (DetourDetach((void**)&Address, (void*)Destination) != NO_ERROR) {
		throw std::runtime_error("IMemory::Restore: Failed to detach hook");
	}

	if (DetourTransactionCommit() != NO_ERROR) {
		throw std::runtime_error("IMemory::Restore: Failed to commit transaction");
	}

	// Success - dismiss the guard so transaction isn't aborted
	transaction_guard.dismiss();
}

void IMemory::HookAPI(void* Address, void* Destination)
{
	// Input validation
	if (!Address || !Destination) {
		throw std::invalid_argument("IMemory::HookAPI: Invalid parameters");
	}

	// Use RAII for transaction management
	auto cleanup_transaction = []() {
		DetourTransactionAbort(); // Abort transaction on failure
		};

	if (DetourTransactionBegin() != NO_ERROR) {
		throw std::runtime_error("IMemory::HookAPI: Failed to begin detour transaction");
	}

	// Use RAII guard to ensure transaction is properly handled
	ScopeGuard transaction_guard(cleanup_transaction);

	if (DetourUpdateThread(GetCurrentThread()) != NO_ERROR) {
		throw std::runtime_error("IMemory::HookAPI: Failed to update thread");
	}

	if (DetourAttach((void**)&Address, (void*)Destination) != NO_ERROR) {
		throw std::runtime_error("IMemory::HookAPI: Failed to attach hook");
	}

	if (DetourTransactionCommit() != NO_ERROR) {
		throw std::runtime_error("IMemory::HookAPI: Failed to commit transaction");
	}

	// Success - dismiss the guard so transaction isn't aborted
	transaction_guard.dismiss();
}

void IMemory::HookAPI(unsigned long Address, unsigned long Destination)
{
	this->HookAPI((void*)Address, (void*)Destination);
}

void IMemory::HookAPI(void* Address, unsigned long Destination)
{
	this->HookAPI(Address, (void*)Destination);
}

void IMemory::HookAPI(unsigned long Address, void* Destination)
{
	this->HookAPI((void*)Address, Destination);
}

Patch::Patch(void* Address, unsigned char* Data, size_t Size, bool Recoverable)
	: m_Address(Address), m_Size(Size), m_Recoverable(Recoverable), m_Original(nullptr)
{
	// Input validation
	if (!Address || !Data || Size == 0) {
		throw std::invalid_argument("Patch::Patch: Invalid parameters");
	}

	Interface<IMemory> Memory;

	try {
		if (Recoverable) {
			// Use RAII for original data backup
			m_OriginalBuffer.reset(new SecureBuffer<unsigned char>(Size));

			// Backup original data safely
			Memory->Copy(m_OriginalBuffer->data(), Address, Size);

			// Set legacy pointer for compatibility (points to RAII-managed memory)
			this->m_Original = m_OriginalBuffer->data();
		}

		// Apply the patch
		Memory->Copy(Address, Data, Size);

	}
	catch (const std::exception& e) {
		// If anything fails, clean up and rethrow
		m_OriginalBuffer.reset();
		this->m_Original = nullptr;
		throw std::runtime_error(std::string("Patch::Patch: Failed to create patch - ") + e.what());
	}
}

Patch::~Patch() noexcept
{
	try {
		if (this->m_Recoverable && this->m_Original && this->m_Address) {
			Interface<IMemory> Memory;

			// Restore original data
			Memory->Copy(this->m_Address, this->m_Original, this->m_Size);
		}

		// RAII buffer automatically cleans up m_OriginalBuffer
		// No manual delete needed!

	}
	catch (...) {
		// Destructor must not throw - silently handle any errors
		// In a real implementation, you might want to log this error
	}
}
