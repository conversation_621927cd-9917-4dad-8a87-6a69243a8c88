//int __fastcall CanAttack(void *Player, void *edx, int Target, int Value)
//{
//	IChar IPlayer(Player);
//	IChar ITarget((void*)Target);
//
//	if (IPlayer.IsOnline() && ITarget.IsOnline())
//	{
//		if (IPlayer.GetType() == 0 && ITarget.GetType() == 0)
//		{
//			//if (IPlayer.IsValid() && ITarget.IsValid() && IPlayer.IsBuff(pvpAreaBuffId) && ITarget.IsBuff(pvpAreaBuffId) && IPlayer.GetPartyID() == ITarget.GetPartyID() && IPlayer.IsParty() && ITarget.IsParty())
//			//	return 0;
//			//if (IPlayer.IsValid() && ITarget.IsValid() && IPlayer.IsBuff(BFBuffT1) && ITarget.IsBuff(BFBuffT2))
//			//	return 1;
//			//if (IPlayer.IsValid() && ITarget.IsValid() && IPlayer.IsBuff(BFBuffT2) && ITarget.IsBuff(BFBuffT1))
//			//	return 1;
//
//		}
//
//		return CPlayer::CanAttack(Player,Target,Value);
//	} else {
//		return 0;
//	}
//}