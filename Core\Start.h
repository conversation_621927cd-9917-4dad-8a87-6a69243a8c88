



void __fastcall Start(int Start, void *edx, u_short hostshort)
{
	
    CIOServer::Start(Start,hostshort);
	CConsole::Blue("[Heth] Awesome addons has been loaded. . .");
	//CConsole::Blue("[Heth] Included -> Boxes, AreaProtection, BossDrop(+RNG), <PERSON>em<PERSON>uff, PK-Kill Notice, AntiAFKLeveling");
	//CConsole::Blue("Raid,Mautareta, Fishing, MonsterSkills, BossSummonDonation & KillToSummon Systems. . .");
	//CConsole::Blue("[Heth] Fixes for Scrolls,Exp Stone, Thief Acrobatic. . .");

}

int __cdecl Black(char *Msg, ...)
{
	std::string GetMsg = std::string(Msg);

	if ( GetMsg.substr(0,2) == "##" || GetMsg.substr(0,2) == "$$" || GetMsg.substr(0,2) == "@@" )
		return 0;


	va_list va;
	va_start(va,Msg);
	int Check = CLog::AddV(1,Msg,va);
	va_end(va);
	return Check;
}

int __cdecl Blue(char *Msg, ...)
{
	std::string GetMsg = std::string(Msg);


	va_list va;
	va_start(va,Msg);
	int Check = CLog::AddV(0,Msg,va);
	va_end(va);
	return Check;
}
