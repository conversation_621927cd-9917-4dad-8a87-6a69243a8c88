
#include "SecureBuffer.h"
#include "MemoryGuard.h"

// Security constants
const size_t MAX_COMMAND_LENGTH = 1024;
const size_t MAX_PLAYERNAME_LENGTH = 64;
const size_t MAX_MESSAGE_LENGTH = 512;
const int MAX_STAT_VALUE = 10000;  // Reasonable limit for stats

// Input validation helper functions
bool IsValidPlayerName(const std::string& name) {
    if (name.empty() || name.length() > MAX_PLAYERNAME_LENGTH) return false;
    // Allow only alphanumeric characters, spaces, and basic punctuation
    for (char c : name) {
        if (!std::isalnum(c) && c != ' ' && c != '_' && c != '-') {
            return false;
        }
    }
    return true;
}

bool IsValidMessage(const std::string& msg) {
    if (msg.empty() || msg.length() > MAX_MESSAGE_LENGTH) return false;
    // Basic message validation - no control characters
    for (char c : msg) {
        if (c < 32 && c != '\t' && c != '\n' && c != '\r') {
            return false;
        }
    }
    return true;
}

bool IsValidAmount(int amount) {
    return amount >= -MAX_STAT_VALUE && amount <= MAX_STAT_VALUE;
}

void __fastcall ChatCommand(int Player, void *edx, const char *command)
{
    // Input validation
    if (!command) {
        return;
    }

    size_t cmd_len = strnlen(command, MAX_COMMAND_LENGTH + 1);
    if (cmd_len == 0 || cmd_len > MAX_COMMAND_LENGTH) {
        return;
    }

    // Use secure buffer for command processing
    SecureCharBuffer secure_cmd(cmd_len + 1);
    secure_cmd.safe_copy_from(command, cmd_len);
    secure_cmd[cmd_len] = '\0';  // Ensure null termination

    std::string cmd(secure_cmd.data());
    IChar IPlayer((void*)Player);

    // Validate player is online for all commands
    if (!IPlayer.IsOnline()) {
        return;
    }

    try {
        // Reload command
        if (IPlayer.GetAdmin() >= 11 && cmd.substr(0,11) == "/hethreload") {
            ReadConfig();
            IPlayer.SystemMessage("Heth Addons reloaded successfully.",TEXTCOLOR_INFOMSG);
            return;
        }

        // Stat modification commands with secure parsing
        if (IPlayer.GetAdmin() >= 7) {
            int amount = 0;

            // EVA command
            if (sscanf_s(secure_cmd.data(), "/addeva %d", &amount) == 1) {
                if (IsValidAmount(amount)) {
                    IPlayer.AddEva(amount);
                    std::string msg = "Your EVA increased by [" + Int2String(amount) + "]";
                    IPlayer.SystemMessage(msg, TEXTCOLOR_INFOMSG);
                }
                return;
            }

            // OTP command
            if (sscanf_s(secure_cmd.data(), "/addotp %d", &amount) == 1) {
                if (IsValidAmount(amount)) {
                    IPlayer.AddOTP(amount);
                    std::string msg = "Your OTP increased by [" + Int2String(amount) + "]";
                    IPlayer.SystemMessage(msg, TEXTCOLOR_INFOMSG);
                }
                return;
            }

            // MP command
            if (sscanf_s(secure_cmd.data(), "/addmp %d", &amount) == 1) {
                if (IsValidAmount(amount)) {
                    IPlayer.IncreaseMaxMp(amount);
                    (*(void(__cdecl**)(void*, signed int, signed int, int))(*(DWORD*)IPlayer.GetOffset() + 88))(IPlayer.GetOffset(), 8, 1, CChar::GetMaxMp((int)IPlayer.GetOffset()));
                    std::string msg = "Your MP increased by [" + Int2String(amount) + "]";
                    IPlayer.SystemMessage(msg, TEXTCOLOR_INFOMSG);
                }
                return;
            }

            // HP command
            if (sscanf_s(secure_cmd.data(), "/addhp %d", &amount) == 1) {
                if (IsValidAmount(amount)) {
                    IPlayer.IncreaseMaxHp(amount);
                    (*(void(__cdecl**)(void*, signed int, signed int, int))(*(DWORD*)IPlayer.GetOffset() + 88))(IPlayer.GetOffset(), 7, 1, CChar::GetMaxHp((int)IPlayer.GetOffset()));
                    std::string msg = "Your HP increased by [" + Int2String(amount) + "]";
                    IPlayer.SystemMessage(msg, TEXTCOLOR_INFOMSG);
                }
                return;
            }

            // Absorb command
            if (sscanf_s(secure_cmd.data(), "/addabs %d", &amount) == 1) {
                if (IsValidAmount(amount)) {
                    IPlayer.AddAbsorb(amount);
                    std::string msg = "Your Absorb increased by [" + Int2String(amount) + "]";
                    IPlayer.SystemMessage(msg, TEXTCOLOR_INFOMSG);
                }
                return;
            }

            // Critical Damage command
            if (sscanf_s(secure_cmd.data(), "/addcd %d", &amount) == 1) {
                if (IsValidAmount(amount)) {
                    IPlayer.IncreaseCritDamage(amount);
                    std::string msg = "Your Critical Damage increased by [" + Int2String(amount) + "]";
                    IPlayer.SystemMessage(msg, TEXTCOLOR_INFOMSG);
                }
                return;
            }

            // Skill Point command
            if (sscanf_s(secure_cmd.data(), "/addsp %d", &amount) == 1) {
                if (IsValidAmount(amount)) {
                    IPlayer.AddSkillPoint(amount);
                    std::string msg = "Your SkillPoint increased by [" + Int2String(amount) + "]";
                    IPlayer.SystemMessage(msg, TEXTCOLOR_INFOMSG);
                }
                return;
            }

            // Stats command
            if (cmd.substr(0, 6) == "/stats") {
                IPlayer.IncreaseStat(255 - IPlayer.GetStr(), 0);
                CDBSocket::Write(16, "dbwbb", IPlayer.GetPID(), 23, IPlayer.GetStatPoint(), 0, *(DWORD*)(Player + 4 * 0 + 64));
                IPlayer.IncreaseStat(255 - IPlayer.GetHth(), 1);
                CDBSocket::Write(16, "dbwbb", IPlayer.GetPID(), 23, IPlayer.GetStatPoint(), 1, *(DWORD*)(Player + 4 * 1 + 64));
                IPlayer.IncreaseStat(255 - IPlayer.GetInt(), 2);
                CDBSocket::Write(16, "dbwbb", IPlayer.GetPID(), 23, IPlayer.GetStatPoint(), 2, *(DWORD*)(Player + 4 * 2 + 64));
                IPlayer.IncreaseStat(255 - IPlayer.GetWis(), 3);
                CDBSocket::Write(16, "dbwbb", IPlayer.GetPID(), 23, IPlayer.GetStatPoint(), 3, *(DWORD*)(Player + 4 * 3 + 64));
                IPlayer.IncreaseStat(255 - IPlayer.GetAgi(), 4);
                CDBSocket::Write(16, "dbwbb", IPlayer.GetPID(), 23, IPlayer.GetStatPoint(), 4, *(DWORD*)(Player + 4 * 4 + 64));
                IPlayer.SystemMessage("Your Base stats Increased to Max", TEXTCOLOR_INFOMSG);
                return;
            }
        }
    }
    catch (const std::exception& e) {
        // Log security error but don't expose details to user
        writeToFile("Security error in ChatCommand: " + std::string(e.what()));
        return;
    }
    catch (...) {
        // Handle any other exceptions
        writeToFile("Unknown error in ChatCommand");
        return;
    }

		// if (IPlayer.IsOnline() && IPlayer.GetAdmin() >= 7 && sscanf(command, "/talk %s %[a-z | A-Z | 0-9/<>|.,~*;`:!^+%&=?_-�#$�]", &cbplayername, &playermsg) == 2)
		// {
		// 	if (!strlen(cbplayername))
		// 		return;

		// 	const char* PlayerName = cbplayername;
		// 	IChar Target((void*)CPlayer::FindPlayerByName((char*)PlayerName));

		// 	if (strlen(playermsg))
		// 		CPlayer::WriteAll(60, "ss", PlayerName, playermsg);
		// 	return;
		// }


		// if (IPlayer.IsOnline() && IPlayer.GetAdmin() >= 3 && cmd.substr(0, 10) == "/RebirthOff")
		// {
		// 	try {
		// 		auto memory = std::make_unique<IMemory>();
		// 		memory->Fill(0x41FFA0, 194, 1);
		// 		memory->Fill(0x41FFA1, 4, 1);
		// 		memory->Fill(0x41FFA2, 0, 1);
		// 		CPlayer::WriteAll(0xFF, "dsd", 247, "Rebirths has been disabled.", 9);
		// 		// memory automatically cleaned up
		// 	}
		// 	catch (const std::exception& e) {
		// 		writeToFile("Error in RebirthOff command: " + std::string(e.what()));
		// 	}
		// 	return;
		// }

		// if (IPlayer.IsOnline() && IPlayer.GetAdmin() >= 3 && cmd.substr(0, 11) == "/RebirthOn")
		// {
		// 	try {
		// 		auto memory = std::make_unique<IMemory>();
		// 		memory->Fill(0x41FFA0, 85, 1);
		// 		memory->Fill(0x41FFA1, 139, 1);
		// 		memory->Fill(0x41FFA2, 236, 1);
		// 		CPlayer::WriteAll(0xFF, "dsd", 247, "Rebirths has been enabled.", 9);
		// 		// memory automatically cleaned up
		// 	}
		// 	catch (const std::exception& e) {
		// 		writeToFile("Error in RebirthOn command: " + std::string(e.what()));
		// 	}
		// 	return;
		// }



	//raid
	if (IPlayer.IsOnline() && IPlayer.GetAdmin() >= 8 && cmd.substr(0,6) == "/xraid" && Raid::Active == false)
	{
		if (Raid::RegisterAmount > 0)
		{
			
			Raid::KillCount = 0;
			Raid::RegisterAmount = 0;
			Raid::Active = true;
			CPlayer::WriteAll(0xFF, "dsd", 247, "Raid System has started.", 2);
			Summon(0, raidMap, raidManagerX, raidManagerY, raidManager, 1, 0, raidTime*1000, raidTime*1000, 0);
			return;
		} else {
			CPlayer::WriteAll(0xFF, "dsd", 247, "Not enough players registered for the raid system.", 2);
			return;
		}
	}

	if (IPlayer.IsOnline() && IPlayer.GetAdmin() >= 8 && cmd.substr(0,9) == "/endxraid" && Raid::Active == true)
	{
		Raid::Active = false;
		return;
	}

	//Mautareta
	//if (IPlayer.IsOnline() && IPlayer.GetAdmin() >= 8 && cmd.substr(0,6) == "/mauta" && Muta::Active == false)
	//{
	//	if (Muta::RegisterAmount > 0)
	//	{
	//		
	//		Muta::Dialogue = 0;
	//		Muta::RegisterAmount = 0;
	//		Muta::Active = true;
	//		CPlayer::WriteAll(0xFF, "dsd", 247, "Mautareta System has started.", 2);
	//		Summon(0, mutaMap, mutaSpawnX, mutaSpawnY, mutaIndex, 1, 0, 0, mutaTime*1000, 0);
	//		return;
	//	} else {
	//		CPlayer::WriteAll(0xFF, "dsd", 247, "Not enough players registered for the Mautareta system.", 2);
	//		return;
	//	}
	//}

	//if (IPlayer.IsOnline() && IPlayer.GetAdmin() >= 8 && cmd.substr(0,9) == "/endmauta" && Muta::Active == true)
	//{
	//	Muta::Active = false;
	//	return;
	//}




	//move to a place
		if (IPlayer.IsOnline() && IPlayer.GetAdmin() >= 3) {
			int tox = 0, toy = 0, toz = 0;
			if (sscanf_s(secure_cmd.data(), "/mc %d %d %d", &toz, &tox, &toy) == 3) {
				// Validate coordinates are within reasonable bounds
				const int MAX_COORD = 1000000;
				const int MIN_COORD = -1000000;

				if (tox >= MIN_COORD && tox <= MAX_COORD &&
					toy >= MIN_COORD && toy <= MAX_COORD &&
					toz >= 0 && toz <= 1000) {  // Map IDs are typically 0-1000

					try {
						IPlayer.Teleport(toz, tox, toy);
					}
					catch (const std::exception& e) {
						writeToFile("Error in teleport command: " + std::string(e.what()));
					}
				}
				return;
			}
		}


// if(IPlayer.IsOnline() && IPlayer.GetAdmin() >= 11 && cmd.substr(0,11) == "/dropreload"){
// 	IPlayer.SystemMessage("ItemGroup has been reloaded.", TEXTCOLOR_INFOMSG);
// 	CObject::ReloadItemGroup();

// return;
// }
		//fish
// 	if(IPlayer.IsOnline() && IPlayer.GetAdmin() >= 11 && cmd.substr(0,8) == "/fish"){
// 		int isBound = FishingCheck.find(1)->second.bound;
// 		int money = FishingCheck.find(1)->second.money;
// 		int exp = FishingCheck.find(1)->second.exp;
// 		int GetSize = FishingCheck.find(1)->second.Items.size();
// 		int Rate = CTools::Rate(1,1000);
// 		bool gotItem = false;
// 		if(IPlayer.GetLevel() >= FishingCheck.find(1)->second.level){
//         //give exp
//         if (exp > 1)
//           ( * (int(__cdecl ** )(int, signed int, signed int, unsigned __int64, unsigned __int64))( * (DWORD * ) IPlayer.GetOffset() + 88))((int) IPlayer.GetOffset(), 25, 1, (unsigned __int64) exp, HIDWORD(exp));
// 		//give money
// 		if (money != 0)
//             CItem::InsertItem((int) IPlayer.GetOffset(), 27, 31, 0, money, -1);
			
// 		for (int i = 0; i < GetSize; i++)
// 			{
// 				int curRG = FishingCheck.find(1)->second.Items[i];
// 				int curIA = FishingCheck.find(1)->second.Amounts[i];
// 				int curIR = FishingCheck.find(1)->second.Rates[i];
				
// 				if(i == 0){
// 					if(Rate > 0 && Rate <= curIR && curRG != 0 && curIA != 0){

// 					if(isBound == 0)
// 						CItem::InsertItem((int)IPlayer.GetOffset(),27,curRG,0,curIA,-1);
// 					if(isBound == 1)
// 						CItem::InsertItem((int)IPlayer.GetOffset(),27,curRG,256,curIA,-1);

// 					gotItem = true;
// 				}
// 				}else{
// 					int prevIR = FishingCheck.find(1)->second.Rates[i-1];

// 					if(Rate > prevIR && Rate <= curIR && curRG != 0 && curIA != 0){
// 						gotItem = true;

// 						if(isBound == 0)
// 							CItem::InsertItem((int)IPlayer.GetOffset(),27,curRG,0,curIA,-1);
// 						if(isBound == 1)
// 							CItem::InsertItem((int)IPlayer.GetOffset(),27,curRG,256,curIA,-1);
						

// 					}
				
// 				}


// 			}
// }else{
// 	gotItem = true;
// 	IPlayer.SystemMessage("Sorry you are low level, and cant fish.", TEXTCOLOR_RED);
// }


// 				if(gotItem == false){
// 					IPlayer.SystemMessage("You are unlucky and caught nothing, good luck next time.", TEXTCOLOR_RED);
// 				}
// 		return;
// 	}



	//disable suicide
	//	if (IPlayer.IsOnline() && cmd.substr(0,8) == "/suicide")
	//{
	//	if(IPlayer.IsOnline() &&
	//	 (IPlayer.IsBuff(BFBuffT1) ||
	//	  IPlayer.IsBuff(BFBuffT2) ||
	//	   IPlayer.IsBuff(jailBuff))){
	//	IPlayer.BoxMsg("You cant suicide because you are inside a system.");
	//	return;
	//}

	//}



	//get raid registered
	if(IPlayer.IsOnline() && IPlayer.GetAdmin() >= 11 && cmd.substr(0,7) == "/rgraid"){
		std::string msg = "Raid Registered Players: " + Int2String(Raid::RegisterAmount);
		IPlayer.SystemMessage(msg,TEXTCOLOR_ALLIANCE);
		return;
	}
	//get mauta registered
	//if(IPlayer.IsOnline() && IPlayer.GetAdmin() >= 11 && cmd.substr(0,8) == "/rgmauta"){
	//	std::string msg = "Mautareta Registered Players: " + Int2String(Muta::RegisterAmount);
	//	IPlayer.SystemMessage(msg,TEXTCOLOR_ALLIANCE);
	//	return;
	//}
		
	//get map
	if(IPlayer.IsOnline() && IPlayer.GetAdmin() >= 11 && cmd.substr(0,8) == "/getmap"){
		std::string msg = "Your map is: " + Int2String(IPlayer.GetMap());
		IPlayer.SystemMessage(msg,TEXTCOLOR_ALLIANCE);
		return;
	}

	//get ip
	//if(IPlayer.IsOnline() && IPlayer.GetAdmin() >= 11 && cmd.substr(0,6) == "/getip"){
	//	std::string msg = "Your ip is: " + IPlayer.GetIP();
	//	IPlayer.SystemMessage(msg,TEXTCOLOR_ALLIANCE);
	//	return;
	//}

	//get react
	if(IPlayer.IsOnline() && IPlayer.GetAdmin() >= 11 && cmd.substr(0,8) == "/getrect"){
		std::string msg = "React X: " + Int2String(IPlayer.GetRectX()) + " React Y: " + Int2String(IPlayer.GetRectY());
		IPlayer.SystemMessage(msg,TEXTCOLOR_ALLIANCE);
		return;
	}

		//set buff icon
	//if (IPlayer.IsOnline() && IPlayer.GetAdmin() >= 11 && sscanf(command, "/seticon %d %d",&type ,&key) == 2){
	//	IPlayer.SetBuffIcon(30000,type,99,key);
	//	return;
	//}

	//battlefield
	//	if (IPlayer.IsOnline() && IPlayer.GetAdmin() >= 8 && cmd.substr(0,6) == "/gvgbf" && Protect32::Active == false)
	//{
	//		if (ProtectLeaderList.size() > 1)
	//		{
	//			std::random_shuffle(ProtectLeaderList.begin(), ProtectLeaderList.end());
	//			Protect32::GuildFirst = ProtectLeaderList.front();
	//			ProtectLeaderList.erase(ProtectLeaderList.begin());
	//			std::random_shuffle(ProtectLeaderList.begin(), ProtectLeaderList.end());
	//			Protect32::GuildSecond = ProtectLeaderList.front();
	//			ProtectLeaderList.erase(ProtectLeaderList.begin());		
	//			Protect32::FirstGuild = ProtectLeaderName.find(Protect32::GuildFirst)->second;
	//			Protect32::SecondGuild = ProtectLeaderName.find(Protect32::GuildSecond)->second;
	//			ProtectLeaderList.clear();
	//			ProtectLeaderName.clear();		
	//			std::string msg = "GvG Battlefield started between ";
	//			msg = msg + Protect32::FirstGuild;
	//			msg = msg + " guild and ";
	//			msg = msg + Protect32::SecondGuild;
	//			msg = msg + " guild.";
	//			CPlayer::WriteAll(0xFF, "dsd", 247, msg.c_str(), 2);
	//			Protect32::RedScore = 0;
	//			Protect32::BlueScore = 0;
	//			Protect32::RedWin = 0;
	//			Protect32::BlueWin = 0;
	//			Protect32::Winner = 0;
	//			Protect32::Time = BFTime;
	//			Protect32::Active = true;
	//		} else {
	//			CPlayer::WriteAll(0xFF, "dsd", 247, "Not enough guilds registered for GvG Battlefield.", 2);
	//		}
	//	return;
	//}


		//cancel buff from a player
	//if (IPlayer.IsOnline() && IPlayer.GetAdmin() >= 11 && sscanf(command, "/cancelbuff %d %[a-z | A-Z | 0-9/<>|.,~*;`:!'^+%&/()=?_-£#${[]}\/€]", &cbuffid, &cbplayername) == 2)
	//{
	//	if (!strlen(cbplayername))
	//		return;

	//	const char *PlayerName = cbplayername;
	//	IChar Target((void*)CPlayer::FindPlayerByName((char*)PlayerName));

	//	if (Target.IsOnline() && cbuffid)
	//	{
	//		std::string name = Target.GetName();
	//		std::string msg =   "Removed a buff from: " + name;
	//		Target.CancelBuff(cbuffid);
	//		IPlayer.SystemMessage(msg.c_str(),TEXTCOLOR_GREEN);

	//	}
	//	return;
	//}


	//	if (IPlayer.IsOnline() && IPlayer.GetAdmin() >= 11 && sscanf(command, "/remainbuff %d", &cbuffid) == 1)
	//{
	//	if (cbuffid == 0)
	//		return;

	//		std::string msg = "Remain: " + IPlayer.GetBuffRemain(cbuffid);
	//		IPlayer.SystemMessage(msg.c_str(),TEXTCOLOR_GREEN);

	//	return;
	//}



	///jail
	//if (IPlayer.IsOnline() && IPlayer.GetAdmin() >= 3 && sscanf(command, "/jail %d %[a-z | A-Z | 0-9/<>|.,~*;`:!'^+%&/()=?_-£#${[]}\/€]", &jailtime, &jailname) == 2)
	//{
	//	if (!strlen(jailname))
	//		return;

	//	const char *PlayerName = jailname;
	//	IChar Target((void*)CPlayer::FindPlayerByName((char*)PlayerName));

	//	if (Target.IsOnline() && jailtime)
	//	{
	//		std::string name = Target.GetName();
	//		std::string msg =   name + " Has been sent to jail for " + Int2String(jailtime) + " Seconds.";
	//		CPlayer::WriteAll(0xFF, "dsd", 247, msg.c_str(), 1);
	//		Target.Buff(jailBuff,jailtime,0);
	//		Jail[Target.GetPID()] = (jailtime*1000)+GetTickCount();
	//		DiscordLog(msg);
	//	}
	//	return;
	//}

	//unjail
	//	if (IPlayer.IsOnline() && IPlayer.GetAdmin() >= 3 && sscanf(command, "/unjail %[a-z | A-Z | 0-9/<>|.,~*;`:!'^+%&/()=?_-£#${[]}\/€]", &jailname) == 1)
	//{
	//	if (!strlen(jailname))
	//		return;

	//	const char *PlayerName = jailname;
	//	IChar Target((void*)CPlayer::FindPlayerByName((char*)PlayerName));

	//	if (Target.IsOnline() && Jail.count(Target.GetPID()) && Jail.find(Target.GetPID())->second)
	//	{
	//		Target.CancelBuff(jailBuff);
	//		Jail.erase(Target.GetPID());
	//		Target.Teleport(0, 257620, 259280);
	//		Target.BoxMsg("Your jail time has ended by the admin and you have to relog to regain your strength.");
	//	}

	//	return;
	//}


	CPlayer::ChatCommand(Player, command);
}