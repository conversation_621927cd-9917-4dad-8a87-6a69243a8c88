void __fastcall Occupation(void *SkillPointer, void *edx)
{
	IChar IPlayer((void*)*(DWORD*)SkillPointer);

	if (IPlayer.IsOnline())
	{

		if (IPlayer.GetClass() == 3 && IPlayer.GetLevel() >= 30 && IPlayer.GetSpecialty() >= 3)
		{
			int pSkill = IPlayer.GetSkillPointer(25);

			if (!pSkill)
			{
				CSkill::LearnSkill(SkillPointer,25);
			}
		}


	}

	CSkill::Occupation(SkillPointer);
}


void __fastcall AutoLearn(void *SkillPointer, void *edx, int Value)
{
	IChar IPlayer((void*)*(DWORD*)SkillPointer);

	if (IPlayer.IsOnline())
	{
	if (IPlayer.GetClass() == 3 && IPlayer.GetLevel() >= 30 && IPlayer.GetSpecialty() >= 3)
		{
			int pSkill = IPlayer.GetSkillPointer(25);

			if (pSkill)
			{
				ISkill xSkill((void*)pSkill);
				int CheckGrade = 0;

				if (IPlayer.GetLevel() >= 50)
					CheckGrade = 5;
				else if (IPlayer.GetLevel() >= 45)
					CheckGrade = 4;
				else if (IPlayer.GetLevel() >= 40)
					CheckGrade = 3;
				else if (IPlayer.GetLevel() >= 35)
					CheckGrade = 2;
				else if (IPlayer.GetLevel() >= 30)
					CheckGrade = 1;

				for (int i = 0; i <= CheckGrade; i++)
				{
					if (xSkill.GetGrade() < 5 && xSkill.GetGrade() + 1 <= CheckGrade)
					{
						CDBSocket::Write(10, "ddbw", IPlayer.GetPID(), 25, xSkill.GetGrade() + 1, 56);
						CPlayer::Write(IPlayer.GetOffset(), 81, "bb", 25, xSkill.GetGrade() + 1);
						*(DWORD*)((int)xSkill.GetOffset() + 8) = xSkill.GetGrade() + 1;
					}
				}
			}
		}
	}

	CPlayer::LevelUpUnknown(SkillPointer,Value);
}