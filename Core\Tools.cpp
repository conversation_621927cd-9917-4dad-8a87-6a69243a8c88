#include <Windows.h>
#include "Tools.h"
#include "Interface.h"
#include "SecureBuffer.h"
#include "MemoryGuard.h"
#include <sstream>
#include <fstream>
#include <string>
#include <algorithm>
#include <iostream>
CREATE_INTERFACE(ITools)

unsigned long ITools::GetCaller(size_t Depth)
{
	unsigned long _Ebp, Addr;

	if (!Depth)
	{
		__asm mov eax, [ebp]
			__asm mov _Ebp, eax
	}
	else {
		__asm mov eax, ebp
		__asm mov _Ebp, eax
	}

	for (size_t i = 0; i < Depth; i++)
	{
		__asm mov eax, _Ebp
		__asm mov eax, [eax]
			__asm mov _Ebp, eax
	}

	__asm mov eax, _Ebp
	__asm mov eax, [eax + 4]
		__asm mov Addr, eax
	return Addr - 5;
}

PIMAGE_NT_HEADERS ITools::GetModuleInfo(HMODULE Module)
{
	PIMAGE_DOS_HEADER DOS_Header = (PIMAGE_DOS_HEADER)Module;
	PIMAGE_NT_HEADERS NT_Header = (PIMAGE_NT_HEADERS)((unsigned)DOS_Header + DOS_Header->e_lfanew);
	return NT_Header;
}

std::string ITools::ostream2string(std::ostream& content)
{
	std::ostringstream& converted = dynamic_cast<std::ostringstream&>(content);
	return converted.str();
}

std::string ITools::GetExecutableFromPath(std::string Path)
{
	size_t sep = Path.find_last_of("\\/");

	if (sep != std::string::npos)
		return Path.substr(sep + 1);
	else
		return Path;
}

char* ITools::_Compile(char* Destination, const char* Format, ...)
{
	va_list Args;
	va_start(Args, Format);
	char* end = this->_Compile(Destination, std::string(Format), Args);
	va_end(Args);

	return end;
}
char* ITools::_Compile(char* Destination, std::string Format, va_list vArgs)
{
	if (!Destination) {
		throw std::invalid_argument("ITools::_Compile: Destination is null");
	}

	unsigned char pTypeByte = 0;
	unsigned short pTypeWord = 0;
	unsigned long pTypeDword = 0;
	float pTypeFloat = 0.0;
	double pTypeDouble = 0.0;
	unsigned __int64 pTypeQword = 0;
	std::string pTypeStr;
	char* pTypeArray = NULL;
	int pTypeArrayLen = 0;

	for (size_t i = 0; i < Format.length(); i++)
	{
		switch (Format[i])
		{
		case 'b':
		case 'B':
			pTypeByte = va_arg(vArgs, unsigned char);
			std::memcpy(Destination, &pTypeByte, sizeof(unsigned char));
			Destination += sizeof(unsigned char);
			break;
		case 'w':
		case 'W':
			pTypeWord = va_arg(vArgs, unsigned short);
			std::memcpy(Destination, &pTypeWord, sizeof(unsigned short));
			Destination += sizeof(unsigned short);
			break;
		case 'd':
		case 'U':
			pTypeDword = va_arg(vArgs, unsigned long);
			std::memcpy(Destination, &pTypeDword, sizeof(unsigned long));
			Destination += sizeof(unsigned long);
			break;
		case 'f':
			pTypeFloat = va_arg(vArgs, float);
			std::memcpy(Destination, &pTypeFloat, sizeof(float));
			Destination += sizeof(float);
			break;
		case 'D':
			pTypeDouble = va_arg(vArgs, double);
			std::memcpy(Destination, &pTypeDouble, sizeof(double));
			Destination += sizeof(double);
			break;
		case 'I':
			pTypeQword = va_arg(vArgs, unsigned __int64);
			std::memcpy((void*)Destination, (void*)&pTypeQword, 8);
			Destination += 8;
			break;
		case 's':
		case 'S':
			pTypeStr = va_arg(vArgs, char*);
			if (pTypeStr.empty()) break;
			std::memcpy(Destination, pTypeStr.c_str(), pTypeStr.length() + 1);
			Destination += pTypeStr.length() + 1;
			break;
		case 'm':
		case 'M':
			pTypeArray = va_arg(vArgs, char*);
			pTypeArrayLen = va_arg(vArgs, int);
			if (pTypeArray && pTypeArrayLen > 0) {
				std::memcpy(Destination, pTypeArray, pTypeArrayLen);
				Destination += pTypeArrayLen;
			}
			break;
		default:
			break;
		}
	}
	return Destination;
}
size_t ITools::GenerateSize(std::string Format, ...)
{
	va_list Args;
	va_start(Args, Format);
	size_t Ret = this->GenerateSize(Format, Args);
	va_end(Args);
	return Ret;
}

__int64 ITools::Per_calculation_int64(int add, __int64 value) {
	__int64 result = (__int64)((double)(add / 100.0) * value);
	return result;
}

size_t ITools::GenerateSize(std::string Format, va_list vArgs)
{
	int PacketSize = 0;
	std::string pSizeStr;

	for (size_t i = 0; i < Format.length(); i++)
	{
		switch (Format[i])
		{
		case 'b':
		case 'B':
			PacketSize += sizeof(unsigned char);
			va_arg(vArgs, unsigned char);
			break;
		case 'w':
		case 'W':
			PacketSize += sizeof(unsigned short);
			va_arg(vArgs, unsigned short);
			break;
		case 'd':
		case 'U':
			PacketSize += sizeof(unsigned long);
			va_arg(vArgs, unsigned long);
			break;
		case 'f':
			PacketSize += sizeof(float);
			va_arg(vArgs, float);
			break;
		case 'D':
			PacketSize += sizeof(double);
			va_arg(vArgs, double);
			break;
		case 'I':
			PacketSize += sizeof(__int64);
			va_arg(vArgs, unsigned __int64);
			break;
		case 's':
		case 'S':
			pSizeStr = va_arg(vArgs, char*);
			PacketSize += pSizeStr.length() + 1;
			break;
		case 'm':
		case 'M':
			va_arg(vArgs, char*);
			PacketSize += va_arg(vArgs, int);
			break;
		default:
			break;
		}
	}

	return PacketSize;
}

void ITools::Compile(char* Destination, int Size, std::string Format, ...)
{
	va_list Args;
	va_start(Args, Format);
	this->Compile(Destination, Size, Format, Args);
	va_end(Args);
}

void ITools::Compile(char* Destination, int Size, std::string Format, va_list vArgs)
{
	if (!Destination || Size <= 0) {
		throw std::invalid_argument("ITools::Compile: Invalid parameters");
	}

	SecureBuffer<char> safe_dest(Size);
	char* current_pos = safe_dest.data();
	char* dest_start = current_pos;

	unsigned char pTypeByte = 0;
	unsigned short pTypeWord = 0;
	unsigned long pTypeDword = 0;
	float pTypeFloat = 0.0;
	double pTypeDouble = 0.0;
	unsigned __int64 pTypeQword = 0;
	std::string pTypeStr;
	char* pTypeArray = NULL;
	int pTypeArrayLen = 0;

	for (size_t i = 0; i < Format.length(); i++)
	{
		switch (Format[i])
		{
		case 'b':
		case 'B':
			if (current_pos - dest_start + sizeof(unsigned char) > Size) break;
			pTypeByte = va_arg(vArgs, unsigned char);
			std::memcpy(current_pos, &pTypeByte, sizeof(unsigned char));
			current_pos += sizeof(unsigned char);
			break;
		case 'w':
		case 'W':
			if (current_pos - dest_start + sizeof(unsigned short) > Size) break;
			pTypeWord = va_arg(vArgs, unsigned short);
			std::memcpy(current_pos, &pTypeWord, sizeof(unsigned short));
			current_pos += sizeof(unsigned short);
			break;
		case 'd':
		case 'U':
			if (current_pos - dest_start + sizeof(unsigned long) > Size) break;
			pTypeDword = va_arg(vArgs, unsigned long);
			std::memcpy(current_pos, &pTypeDword, sizeof(unsigned long));
			current_pos += sizeof(unsigned long);
			break;
		case 'f':
			if (current_pos - dest_start + sizeof(float) > Size) break;
			pTypeFloat = va_arg(vArgs, float);
			std::memcpy(current_pos, &pTypeFloat, sizeof(float));
			current_pos += sizeof(float);
			break;
		case 'D':
			if (current_pos - dest_start + sizeof(double) > Size) break;
			pTypeDouble = va_arg(vArgs, double);
			std::memcpy(current_pos, &pTypeDouble, sizeof(double));
			current_pos += sizeof(double);
			break;
		case 'I':
			if (current_pos - dest_start + 8 > Size) break;
			pTypeQword = va_arg(vArgs, unsigned __int64);
			std::memcpy(current_pos, &pTypeQword, 8);
			current_pos += 8;
			break;
		case 's':
		case 'S':
			pTypeStr = va_arg(vArgs, char*);
			if (!pTypeStr.empty() && current_pos - dest_start + pTypeStr.length() + 1 <= Size) {
				std::memcpy(current_pos, pTypeStr.c_str(), pTypeStr.length() + 1);
				current_pos += pTypeStr.length() + 1;
			}
			break;
		case 'm':
		case 'M':
			pTypeArray = va_arg(vArgs, char*);
			pTypeArrayLen = va_arg(vArgs, int);
			if (pTypeArray && pTypeArrayLen > 0 && current_pos - dest_start + pTypeArrayLen <= Size) {
				std::memcpy(current_pos, pTypeArray, pTypeArrayLen);
				current_pos += pTypeArrayLen;
			}
			break;
		default:
			break;
		}
	}

	safe_dest.safe_copy_to(Destination, Size);
}

char* ITools::Compile(char* Destination, std::string Format, ...)
{
	va_list Args;
	va_start(Args, Format);
	char* end = this->Compile(Destination, Format, Args);
	va_end(Args);
	return end;
}

char* ITools::Compile(char* Destination, std::string Format, va_list vArgs)
{
	if (!Destination) {
		throw std::invalid_argument("ITools::Compile: Destination is null");
	}

	unsigned char pTypeByte = 0;
	unsigned short pTypeWord = 0;
	unsigned long pTypeDword = 0;
	float pTypeFloat = 0.0;
	double pTypeDouble = 0.0;
	unsigned __int64 pTypeQword = 0;
	std::string pTypeStr;
	char* pTypeArray = NULL;
	int pTypeArrayLen = 0;

	for (size_t i = 0; i < Format.length(); i++)
	{
		switch (Format[i])
		{
		case 'b':
		case 'B':
			pTypeByte = va_arg(vArgs, unsigned char);
			std::memcpy(Destination, &pTypeByte, sizeof(unsigned char));
			Destination += sizeof(unsigned char);
			break;
		case 'w':
		case 'W':
			pTypeWord = va_arg(vArgs, unsigned short);
			std::memcpy(Destination, &pTypeWord, sizeof(unsigned short));
			Destination += sizeof(unsigned short);
			break;
		case 'd':
		case 'U':
			pTypeDword = va_arg(vArgs, unsigned long);
			std::memcpy(Destination, &pTypeDword, sizeof(unsigned long));
			Destination += sizeof(unsigned long);
			break;
		case 'f':
			pTypeFloat = va_arg(vArgs, float);
			std::memcpy(Destination, &pTypeFloat, sizeof(float));
			Destination += sizeof(float);
			break;
		case 'D':
			pTypeDouble = va_arg(vArgs, double);
			std::memcpy(Destination, &pTypeDouble, sizeof(double));
			Destination += sizeof(double);
			break;
		case 'I':
			pTypeQword = va_arg(vArgs, unsigned __int64);
			std::memcpy((void*)Destination, (void*)&pTypeQword, 8);
			Destination += 8;
			break;
		case 's':
		case 'S':
			pTypeStr = va_arg(vArgs, char*);
			if (!pTypeStr.empty()) {
				std::memcpy(Destination, pTypeStr.c_str(), pTypeStr.length() + 1);
				Destination += pTypeStr.length() + 1;
			}
			break;
		case 'm':
		case 'M':
			pTypeArray = va_arg(vArgs, char*);
			pTypeArrayLen = va_arg(vArgs, int);
			if (pTypeArray && pTypeArrayLen > 0) {
				std::memcpy(Destination, pTypeArray, pTypeArrayLen);
				Destination += pTypeArrayLen;
			}
			break;
		default:
			break;
		}
	}

	return Destination;
}

char* ITools::ParseData(char* Data, char* Format, ...)
{
	if (!Data || !Format) {
		throw std::invalid_argument("ITools::ParseData: Invalid parameters");
	}

	va_list vArgs;
	va_start(vArgs, Format);

	try {
		for (unsigned int i = 0; i < strlen(Format); i++)
		{
			switch (Format[i])
			{
			case 'b':
			{
				unsigned char* ptr = va_arg(vArgs, unsigned char*);
				if (ptr) *ptr = *(unsigned char*)Data;
				Data += sizeof(unsigned char);
			}
			break;
			case 'w':
			{
				unsigned short* ptr = va_arg(vArgs, unsigned short*);
				if (ptr) *ptr = *(unsigned short*)Data;
				Data += sizeof(unsigned short);
			}
			break;
			case 'd':
			{
				unsigned long* ptr = va_arg(vArgs, unsigned long*);
				if (ptr) *ptr = *(unsigned long*)Data;
				Data += sizeof(unsigned long);
			}
			break;
			case 'f':
			{
				float* ptr = va_arg(vArgs, float*);
				if (ptr) *ptr = *(float*)Data;
				Data += sizeof(float);
			}
			break;
			case 'D':
			{
				double* ptr = va_arg(vArgs, double*);
				if (ptr) *ptr = *(double*)Data;
				Data += sizeof(double);
			}
			break;
			case 'I':
			{
				__int64* ptr = va_arg(vArgs, __int64*);
				if (ptr) *ptr = *(__int64*)Data;
				Data += sizeof(__int64);
			}
			break;
			case 's':
			{
				char** ptr = va_arg(vArgs, char**);
				if (ptr) *ptr = Data;
				Data += strlen(Data) + 1;
			}
			break;
			case 'm':
			{
				unsigned char** ptr = va_arg(vArgs, unsigned char**);
				if (ptr) *ptr = (unsigned char*)Data;
				Data += va_arg(vArgs, unsigned long);
			}
			break;
			case '?':
				Data += va_arg(vArgs, unsigned long);
				break;
			}
		}
	}
	catch (...) {
		va_end(vArgs);
		throw;
	}

	va_end(vArgs);
	return Data;
}

std::vector<std::string> ITools::Explode(std::string String, std::string Separator)
{
	std::vector<std::string> r;
	int p = String.find(Separator);

	while (p != std::string::npos)
	{
		if (p) r.push_back(String.substr(0, p));
		String = String.substr(p + Separator.length());
		p = String.find(Separator);
	}

	if (String.length()) r.push_back(String);
	return r;
}

void ITools::SetMemoryEx(void* Destination, const char* Data, size_t Size)
{
	this->MemcpyExD(Destination, (void*)Data, Size);
}

void ITools::SetMemoryEx(unsigned long Destination, const char* Data, size_t Size)
{
	this->SetMemoryEx((void*)Destination, Data, Size);
}

void* ITools::MemcpyEx(void* Dest, void* Source, size_t Size)
{
	if (!Dest || !Source || Size == 0) {
		throw std::invalid_argument("ITools::MemcpyEx: Invalid parameters");
	}

	unsigned long oldSourceProt, oldDestProt;

	auto restore_source = [Source, Size, &oldSourceProt]() {
		VirtualProtect(Source, Size, oldSourceProt, &oldSourceProt);
		};

	auto restore_dest = [Dest, Size, &oldDestProt]() {
		VirtualProtect(Dest, Size, oldDestProt, &oldDestProt);
		};

	if (!VirtualProtect(Source, Size, PAGE_EXECUTE_READWRITE, &oldSourceProt)) {
		throw std::runtime_error("ITools::MemcpyEx: Failed to protect source memory");
	}
	ScopeGuard source_guard(restore_source);

	if (!VirtualProtect(Dest, Size, PAGE_EXECUTE_READWRITE, &oldDestProt)) {
		throw std::runtime_error("ITools::MemcpyEx: Failed to protect destination memory");
	}
	ScopeGuard dest_guard(restore_dest);

	std::memcpy(Dest, Source, Size);
	return Dest;
}

void* ITools::MemcpyExS(void* Dest, void* Source, size_t Size)
{
	if (!Dest || !Source || Size == 0) {
		throw std::invalid_argument("ITools::MemcpyExS: Invalid parameters");
	}

	unsigned long oldSourceProt;

	auto restore_source = [Source, Size, &oldSourceProt]() {
		VirtualProtect(Source, Size, oldSourceProt, &oldSourceProt);
		};

	if (!VirtualProtect(Source, Size, PAGE_EXECUTE_READWRITE, &oldSourceProt)) {
		throw std::runtime_error("ITools::MemcpyExS: Failed to protect source memory");
	}
	ScopeGuard source_guard(restore_source);

	std::memcpy(Dest, Source, Size);
	return Dest;
}

void* ITools::MemcpyExD(void* Dest, void* Source, size_t Size)
{
	if (!Dest || !Source || Size == 0) {
		throw std::invalid_argument("ITools::MemcpyExD: Invalid parameters");
	}

	unsigned long oldDestProt;

	auto restore_dest = [Dest, Size, &oldDestProt]() {
		VirtualProtect(Dest, Size, oldDestProt, &oldDestProt);
		};

	if (!VirtualProtect((LPVOID)Dest, Size, PAGE_EXECUTE_READWRITE, &oldDestProt)) {
		throw std::runtime_error("ITools::MemcpyExD: Failed to protect destination memory");
	}
	ScopeGuard dest_guard(restore_dest);

	std::memcpy(Dest, Source, Size);
	return Dest;
}

void ITools::FillMemoryEx(void* Destination, unsigned char Fill, size_t Size)
{
	if (!Destination || Size == 0) {
		throw std::invalid_argument("ITools::FillMemoryEx: Invalid parameters");
	}

	unsigned long oldDestProt;

	auto restore_dest = [Destination, Size, &oldDestProt]() {
		VirtualProtect(Destination, Size, oldDestProt, &oldDestProt);
		};

	if (!VirtualProtect(Destination, Size, PAGE_EXECUTE_READWRITE, &oldDestProt)) {
		throw std::runtime_error("ITools::FillMemoryEx: Failed to protect destination memory");
	}
	ScopeGuard dest_guard(restore_dest);

	FillMemory(Destination, Size, Fill);
}

void ITools::FillMemoryEx(unsigned long Destination, unsigned char Fill, size_t Size)
{
	this->FillMemoryEx((void*)Destination, Fill, Size);
}

unsigned long ITools::Intercept(unsigned char instruction, void* source, void* destination, size_t length)
{
	if (!source || !destination || length == 0) {
		throw std::invalid_argument("ITools::Intercept: Invalid parameters");
	}

	unsigned long realTarget = 0;

	try {
		SecureBuffer<BYTE> buffer(length, static_cast<BYTE>(0x90));

		if (instruction != ITools::_I_NOP && length >= 5)
		{
			buffer[length - 5] = instruction;
			unsigned long dwJMP = (unsigned long)destination - ((unsigned long)source + 5 + (length - 5));
			std::memcpy(&realTarget, (void*)((unsigned long)source + 1), 4);
			realTarget = realTarget + (unsigned long)source + 5;
			buffer.safe_copy_from(reinterpret_cast<const BYTE*>(&dwJMP), 4, 1 + (length - 5));
		}

		if (instruction == ITools::_I_JE_SHORT)
		{
			buffer[0] = instruction;
			buffer[1] = (BYTE)destination;
		}

		if (instruction == 0x00)
			buffer[0] = (BYTE)destination;

		this->MemcpyExD(source, buffer.data(), length);

	}
	catch (const std::exception& e) {
		throw std::runtime_error(std::string("ITools::Intercept: ") + e.what());
	}

	return realTarget;
}


std::string ITools::removeWhitespace(std::string input)
{
	if (input.length() > 0) {
		input.erase(std::remove(input.begin(), input.end(), ' '), input.end());
		input.erase(std::remove(input.begin(), input.end(), '\t'), input.end());
	}
	return input;
}

std::string ITools::replaceAll(std::string str, std::string from, std::string to) {
	size_t start_pos = 0;
	while ((start_pos = str.find(from, start_pos)) != std::string::npos) {
		str.replace(start_pos, from.length(), to);
		start_pos += to.length();
	}
	return str;
}

std::string ITools::tolower(std::string str) {
	if (str.length() == 0)
		return "";
	std::string sourceString = str;
	std::string destinationString;

	destinationString.resize(sourceString.size());

	std::transform(sourceString.begin(), sourceString.end(), destinationString.begin(), ::tolower);
	return destinationString;
}