/**
 * @file DatabaseExamples.h
 * @brief Examples of how to use the SimpleDatabase system for various game systems
 *
 * This file provides examples of how to integrate the SimpleDatabase system
 * into different game systems. Each example shows the pattern for saving
 * and reading data using the new database format.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 */

#ifndef __DATABASE_EXAMPLES_H
#define __DATABASE_EXAMPLES_H

#include "SimpleDatabase.h"
#include "Core.cpp" // For writeToFile and Int2String functions

// ============================================================================
// EXAMPLE 1: BOSS DONATION SYSTEM
// ============================================================================

/**
 * @brief Save boss donation progress
 * @param bossIndex Index of the boss
 * @param donationAmount Amount donated
 */
void saveBossDonation(int bossIndex, int donationAmount) {
    // Validate input parameters
    if (bossIndex < 0 || bossIndex > 99999 || donationAmount < 0 || donationAmount > 999999) {
        writeToFile("Invalid parameters in saveBossDonation: bossIndex=" + Int2String(bossIndex) + ", donationAmount=" + Int2String(donationAmount));
        return;
    }

    // Use the simple database system
    if (!SaveToSystemDB("bossdonation", bossIndex, donationAmount)) {
        writeToFile("Error in saveBossDonation: Failed to save to database for bossIndex=" + Int2String(bossIndex));
    }
}

/**
 * @brief Read boss donation progress
 * @param bossIndex Index of the boss
 * @return Current donation amount for the boss
 */
int readBossDonation(int bossIndex) {
    return ReadFromSystemDB("bossdonation", bossIndex, 0);
}

// ============================================================================
// EXAMPLE 2: PLAYER STATISTICS SYSTEM
// ============================================================================

/**
 * @brief Save player kill count
 * @param playerID Player ID
 * @param killCount Number of kills
 */
void savePlayerKills(int playerID, int killCount) {
    if (playerID < 0 || playerID > 99999 || killCount < 0 || killCount > 999999) {
        writeToFile("Invalid parameters in savePlayerKills: playerID=" + Int2String(playerID) + ", killCount=" + Int2String(killCount));
        return;
    }

    if (!SaveToSystemDB("playerkills", playerID, killCount)) {
        writeToFile("Error in savePlayerKills: Failed to save to database for playerID=" + Int2String(playerID));
    }
}

/**
 * @brief Read player kill count
 * @param playerID Player ID
 * @return Number of kills for the player
 */
int readPlayerKills(int playerID) {
    return ReadFromSystemDB("playerkills", playerID, 0);
}

/**
 * @brief Increment player kill count
 * @param playerID Player ID
 */
void incrementPlayerKills(int playerID) {
    int currentKills = readPlayerKills(playerID);
    savePlayerKills(playerID, currentKills + 1);
}

// ============================================================================
// EXAMPLE 3: QUEST PROGRESS SYSTEM
// ============================================================================

/**
 * @brief Save quest progress
 * @param questID Quest ID
 * @param progress Progress value (0-100 typically)
 */
void saveQuestProgress(int questID, int progress) {
    if (questID < 0 || questID > 99999 || progress < 0 || progress > 999999) {
        writeToFile("Invalid parameters in saveQuestProgress: questID=" + Int2String(questID) + ", progress=" + Int2String(progress));
        return;
    }

    if (!SaveToSystemDB("questprogress", questID, progress)) {
        writeToFile("Error in saveQuestProgress: Failed to save to database for questID=" + Int2String(questID));
    }
}

/**
 * @brief Read quest progress
 * @param questID Quest ID
 * @return Progress value for the quest
 */
int readQuestProgress(int questID) {
    return ReadFromSystemDB("questprogress", questID, 0);
}

/**
 * @brief Check if quest is completed
 * @param questID Quest ID
 * @param completionThreshold Threshold for completion (default 100)
 * @return True if quest is completed
 */
bool isQuestCompleted(int questID, int completionThreshold = 100) {
    return readQuestProgress(questID) >= completionThreshold;
}

// ============================================================================
// EXAMPLE 4: ITEM COLLECTION SYSTEM
// ============================================================================

/**
 * @brief Save item collection count
 * @param itemID Item ID
 * @param collectedAmount Amount collected
 */
void saveItemCollection(int itemID, int collectedAmount) {
    if (itemID < 0 || itemID > 99999 || collectedAmount < 0 || collectedAmount > 999999) {
        writeToFile("Invalid parameters in saveItemCollection: itemID=" + Int2String(itemID) + ", collectedAmount=" + Int2String(collectedAmount));
        return;
    }

    if (!SaveToSystemDB("itemcollection", itemID, collectedAmount)) {
        writeToFile("Error in saveItemCollection: Failed to save to database for itemID=" + Int2String(itemID));
    }
}

/**
 * @brief Read item collection count
 * @param itemID Item ID
 * @return Amount collected for the item
 */
int readItemCollection(int itemID) {
    return ReadFromSystemDB("itemcollection", itemID, 0);
}

/**
 * @brief Add to item collection
 * @param itemID Item ID
 * @param amountToAdd Amount to add
 */
void addToItemCollection(int itemID, int amountToAdd) {
    if (amountToAdd <= 0) return;
    
    int currentAmount = readItemCollection(itemID);
    int newAmount = currentAmount + amountToAdd;
    
    // Prevent overflow
    if (newAmount > 999999) {
        newAmount = 999999;
    }
    
    saveItemCollection(itemID, newAmount);
}

// ============================================================================
// EXAMPLE 5: RAID SYSTEM INTEGRATION
// ============================================================================

/**
 * @brief Save raid participation count
 * @param raidID Raid ID
 * @param participationCount Number of participations
 */
void saveRaidParticipation(int raidID, int participationCount) {
    if (raidID < 0 || raidID > 99999 || participationCount < 0 || participationCount > 999999) {
        writeToFile("Invalid parameters in saveRaidParticipation: raidID=" + Int2String(raidID) + ", participationCount=" + Int2String(participationCount));
        return;
    }

    if (!SaveToSystemDB("raidparticipation", raidID, participationCount)) {
        writeToFile("Error in saveRaidParticipation: Failed to save to database for raidID=" + Int2String(raidID));
    }
}

/**
 * @brief Read raid participation count
 * @param raidID Raid ID
 * @return Number of participations for the raid
 */
int readRaidParticipation(int raidID) {
    return ReadFromSystemDB("raidparticipation", raidID, 0);
}

// ============================================================================
// UTILITY FUNCTIONS FOR MIGRATION
// ============================================================================

/**
 * @brief Migrate data from old INI-style files to new database system
 * @param systemName Name of the system
 * @param oldFilePath Path to the old INI file
 * @param sectionName Section name in the INI file
 * @param keyName Key name in the INI file
 * @param dataKey Key to use in the new database
 */
void migrateFromINI(const std::string& systemName, const std::string& oldFilePath, 
                   const std::string& sectionName, const std::string& keyName, int dataKey) {
    try {
        // Read from old INI file
        int oldValue = GetPrivateProfileIntA(sectionName.c_str(), keyName.c_str(), 0, oldFilePath.c_str());
        
        // Save to new database system
        if (oldValue > 0) {
            if (!SaveToSystemDB(systemName, dataKey, oldValue)) {
                writeToFile("Failed to migrate data from " + oldFilePath + " to " + systemName + ".db");
            }
        }
    }
    catch (const std::exception& e) {
        writeToFile("Error during migration: " + std::string(e.what()));
    }
}

/**
 * @brief Example of how to read configuration and initialize database
 * This shows the pattern for integrating with ReadConfig.h
 */
void exampleConfigIntegration() {
    // Example: Reading from a configuration file and storing in database
    // This would typically be called from ReadConfig.h
    
    FILE *configFile = fopen("./HethCFG/ExampleSystem.txt", "r");
    if (configFile != NULL) {
        char line[BUFSIZ];
        while (fgets(line, sizeof line, configFile) != NULL) {
            int systemID = 0, value = 0;
            
            // Parse configuration line
            if (sscanf(line, "(example (SystemID %d)(Value %d))", &systemID, &value) == 2) {
                // Store configuration in database
                if (!SaveToSystemDB("examplesystem", systemID, value)) {
                    writeToFile("Failed to save example system configuration for ID: " + Int2String(systemID));
                }
                
                // Later, read it back
                int storedValue = ReadFromSystemDB("examplesystem", systemID, 0);
                // Use storedValue in your system...
            }
        }
        fclose(configFile);
    }
}

#endif // __DATABASE_EXAMPLES_H
