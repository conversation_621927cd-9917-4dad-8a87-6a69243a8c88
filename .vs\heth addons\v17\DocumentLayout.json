{"Version": 1, "WorkspaceRootPath": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\ReadConfig.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\ReadConfig.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\PKKill.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\PKKill.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Summon.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Summon.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Core.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Core.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Player.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Player.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\ItemUse.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\ItemUse.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Command.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Command.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Timer.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Timer.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\FinalDamage.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\FinalDamage.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Quest.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Quest.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\ApplyDamage.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\ApplyDamage.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\GenMonster.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\GenMonster.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\IChar.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\IChar.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\IQuest.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\IQuest.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\IQuest.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\IQuest.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Interface.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Interface.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\CutDownExp.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\CutDownExp.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Packet.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Packet.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\IChar.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\IChar.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Functions.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\Functions.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\base64.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}|Core\\Core.vcxproj|solutionrelative:Core\\base64.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 7, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "PKKill.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\PKKill.h", "RelativeDocumentMoniker": "Core\\PKKill.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\PKKill.h", "RelativeToolTip": "Core\\PKKill.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T05:45:38.871Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ItemUse.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\ItemUse.h", "RelativeDocumentMoniker": "Core\\ItemUse.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\ItemUse.h", "RelativeToolTip": "Core\\ItemUse.h", "ViewState": "AgIAAEcAAAAAAAAAAAAawFIAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T05:33:52.108Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "Timer.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Timer.h", "RelativeDocumentMoniker": "Core\\Timer.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Timer.h", "RelativeToolTip": "Core\\Timer.h", "ViewState": "AgIAAD4AAAAAAAAAAAAawEwAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T05:16:41.05Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "FinalDamage.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\FinalDamage.h", "RelativeDocumentMoniker": "Core\\FinalDamage.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\FinalDamage.h", "RelativeToolTip": "Core\\FinalDamage.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T05:15:32.269Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "Quest.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Quest.h", "RelativeDocumentMoniker": "Core\\Quest.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Quest.h", "RelativeToolTip": "Core\\Quest.h", "ViewState": "AgIAABQAAAAAAAAAAAAawCEAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T05:04:54.413Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "ApplyDamage.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\ApplyDamage.h", "RelativeDocumentMoniker": "Core\\ApplyDamage.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\ApplyDamage.h", "RelativeToolTip": "Core\\ApplyDamage.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T05:03:43.789Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "GenMonster.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\GenMonster.h", "RelativeDocumentMoniker": "Core\\GenMonster.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\GenMonster.h", "RelativeToolTip": "Core\\GenMonster.h", "ViewState": "AgIAAB0AAAAAAAAAAAAawCoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T05:02:05.144Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "ReadConfig.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\ReadConfig.h", "RelativeDocumentMoniker": "Core\\ReadConfig.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\ReadConfig.h", "RelativeToolTip": "Core\\ReadConfig.h", "ViewState": "AgIAABkBAAAAAAAAAAAuwFABAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T04:26:02.546Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "IQuest.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\IQuest.h", "RelativeDocumentMoniker": "Core\\IQuest.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\IQuest.h", "RelativeToolTip": "Core\\IQuest.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T04:17:33.937Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "IQuest.cpp", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\IQuest.cpp", "RelativeDocumentMoniker": "Core\\IQuest.cpp", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\IQuest.cpp", "RelativeToolTip": "Core\\IQuest.cpp", "ViewState": "AgIAAA0AAAAAAAAAAAAqwDMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-05-30T04:17:13.049Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "Interface.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Interface.h", "RelativeDocumentMoniker": "Core\\Interface.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Interface.h", "RelativeToolTip": "Core\\Interface.h", "ViewState": "AgIAAK4AAAAAAAAAAAAqwNIAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T04:16:27.272Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "CutDownExp.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\CutDownExp.h", "RelativeDocumentMoniker": "Core\\CutDownExp.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\CutDownExp.h", "RelativeToolTip": "Core\\CutDownExp.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T04:07:41.084Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "Packet.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Packet.h", "RelativeDocumentMoniker": "Core\\Packet.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Packet.h", "RelativeToolTip": "Core\\Packet.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T04:04:12.604Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "Functions.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Functions.h", "RelativeDocumentMoniker": "Core\\Functions.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Functions.h", "RelativeToolTip": "Core\\Functions.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAGMBAAB8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T04:02:27.835Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "IChar.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\IChar.h", "RelativeDocumentMoniker": "Core\\IChar.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\IChar.h", "RelativeToolTip": "Core\\IChar.h", "ViewState": "AgIAAJgAAAAAAAAAAAApwLcAAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T03:45:14.112Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "Command.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Command.h", "RelativeDocumentMoniker": "Core\\Command.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Command.h", "RelativeToolTip": "Core\\Command.h", "ViewState": "AgIAAHEAAAAAAAAAAAAawIEAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T03:45:02.163Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "IChar.cpp", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\IChar.cpp", "RelativeDocumentMoniker": "Core\\IChar.cpp", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\IChar.cpp", "RelativeToolTip": "Core\\IChar.cpp", "ViewState": "AgIAAJMFAAAAAAAAAAAawKMFAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-05-30T03:44:32.361Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "Summon.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Summon.h", "RelativeDocumentMoniker": "Core\\Summon.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Summon.h", "RelativeToolTip": "Core\\Summon.h", "ViewState": "AgIAAGsAAAAAAAAAAAAqwHgAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T03:41:03.02Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Player.h", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Player.h", "RelativeDocumentMoniker": "Core\\Player.h", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Player.h", "RelativeToolTip": "Core\\Player.h", "ViewState": "AgIAABoBAAAAAAAAAAAawCUBAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-05-30T03:35:54.291Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "Core.cpp", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Core.cpp", "RelativeDocumentMoniker": "Core\\Core.cpp", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\Core.cpp", "RelativeToolTip": "Core\\Core.cpp", "ViewState": "AgIAAFUAAAAAAAAAAAAawF8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-05-30T03:35:21.265Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "base64.cpp", "DocumentMoniker": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\base64.cpp", "RelativeDocumentMoniker": "Core\\base64.cpp", "ToolTip": "G:\\Kal\\New Project\\Cleaning Sources\\Sources\\Heth Addon 2019\\Core\\base64.cpp", "RelativeToolTip": "Core\\base64.cpp", "ViewState": "AgIAACgAAAAAAAAAAAAgwE0AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-05-30T03:34:44.255Z"}]}]}]}