int __fastcall SkillUpgradeCheck(void *Player,int SkillID, int PointerCheck)
{
	IChar IPlayer(Player); int SkillPointer = 0;

	if (IPlayer.IsOnline())
	{
		if (IPlayer.GetClass() == 1 && SkillID == 26)
            {
				int SkillPointer = *((DWORD*)((int)Player + 624) + SkillID + 2);
                    ISkill xSkill((void*)SkillPointer);
                    if (xSkill.GetGrade() >= 4){
						  CDBSocket::Write(10, "ddbw", IPlayer.GetPID(), SkillID, 3,56);
                           CPlayer::Write(IPlayer.GetOffset(), 81, "bb", SkillID, 3);
                            *(DWORD*)((int)xSkill.GetOffset() + 8) = 3;
					}
						

            }


	}

	return 0;
}