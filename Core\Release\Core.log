﻿  Core.cpp
G:\Kal\New Project\Cleaning Sources\Sources\Heth Addon 2019\Core\ReadConfig.h(505,9): warning C4101: 'skilleffect': unreferenced local variable
  (compiling source file 'Core.cpp')
  
G:\Kal\New Project\Cleaning Sources\Sources\Heth Addon 2019\Core\ReadConfig.h(502,156): warning C4101: 'animationSkillDelay': unreferenced local variable
  (compiling source file 'Core.cpp')
  
G:\Kal\New Project\Cleaning Sources\Sources\Heth Addon 2019\Core\ReadConfig.h(504,9): warning C4101: 'debuffeffect': unreferenced local variable
  (compiling source file 'Core.cpp')
  
G:\Kal\New Project\Cleaning Sources\Sources\Heth Addon 2019\Core\Summon.h(112,34): warning C4101: 'e': unreferenced local variable
  (compiling source file 'Core.cpp')
  
G:\Kal\New Project\Cleaning Sources\Sources\Heth Addon 2019\Core\Summon.h(131,34): warning C4101: 'e': unreferenced local variable
  (compiling source file 'Core.cpp')
  
G:\Kal\New Project\Cleaning Sources\Sources\Heth Addon 2019\Core\Summon.h(516,34): warning C4101: 'e': unreferenced local variable
  (compiling source file 'Core.cpp')
  
G:\Kal\New Project\Cleaning Sources\Sources\Heth Addon 2019\Core\GenMonster.h(46,34): warning C4101: 'e': unreferenced local variable
  (compiling source file 'Core.cpp')
  
G:\Kal\New Project\Cleaning Sources\Sources\Heth Addon 2019\Core\GenMonster.h(329,34): warning C4101: 'e': unreferenced local variable
  (compiling source file 'Core.cpp')
  
G:\Kal\New Project\Cleaning Sources\Sources\Heth Addon 2019\Core\GenMonster.h(517,34): warning C4101: 'e': unreferenced local variable
  (compiling source file 'Core.cpp')
  
     Creating library G:\Kal\New Project\Cleaning Sources\Sources\Heth Addon 2019\Release\Heth Addons.lib and object G:\Kal\New Project\Cleaning Sources\Sources\Heth Addon 2019\Release\Heth Addons.exp
  Generating code
  1 of 2686 functions (<0.1%) were compiled, the rest were copied from previous compilation.
    0 functions were new in current compilation
    2 functions had inline decision re-evaluated but remain unchanged
  Finished generating code
  Core.vcxproj -> G:\Kal\New Project\Cleaning Sources\Sources\Heth Addon 2019\Release\Heth Addons.dll
