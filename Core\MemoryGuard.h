/**
 * @file MemoryGuard.h
 * @brief Additional memory safety utilities and RAII wrappers
 *
 * This file provides additional memory safety utilities that complement
 * the SecureBuffer class, including RAII wrappers for various memory
 * management scenarios and automatic cleanup helpers.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 */

#ifndef __MEMORY_GUARD_H
#define __MEMORY_GUARD_H

#include <Windows.h>
#include <memory>
#include <functional>
#include <cstring>
#include "Lock.h"
#include "MutexMap.h"

 /**
  * @brief RAII wrapper for automatic resource cleanup
  *
  * This class provides automatic cleanup of any resource using
  * a custom cleanup function. Useful for C-style APIs and
  * legacy code integration.
  */
template<typename T>
class ResourceGuard {
private:
    T m_resource;
    std::function<void(T)> m_cleanup;
    bool m_released;

public:
    /**
     * @brief Constructor with resource and cleanup function
     * @param resource Resource to manage
     * @param cleanup Function to call for cleanup
     */
    ResourceGuard(T resource, std::function<void(T)> cleanup)
        : m_resource(resource), m_cleanup(cleanup), m_released(false) {
    }

    /**
     * @brief Destructor - automatically calls cleanup
     */
    ~ResourceGuard() {
        if (!m_released && m_cleanup) {
            m_cleanup(m_resource);
        }
    }

    // Non-copyable
    ResourceGuard(const ResourceGuard&) = delete;
    ResourceGuard& operator=(const ResourceGuard&) = delete;

    // Movable
    ResourceGuard(ResourceGuard&& other) noexcept
        : m_resource(other.m_resource), m_cleanup(std::move(other.m_cleanup)), m_released(other.m_released) {
        other.m_released = true;
    }

    ResourceGuard& operator=(ResourceGuard&& other) noexcept {
        if (this != &other) {
            if (!m_released && m_cleanup) {
                m_cleanup(m_resource);
            }
            m_resource = other.m_resource;
            m_cleanup = std::move(other.m_cleanup);
            m_released = other.m_released;
            other.m_released = true;
        }
        return *this;
    }

    /**
     * @brief Get the managed resource
     * @return The managed resource
     */
    T get() const noexcept { return m_resource; }

    /**
     * @brief Release the resource without cleanup
     * @return The released resource
     */
    T release() noexcept {
        m_released = true;
        return m_resource;
    }

    /**
     * @brief Check if resource is still managed
     * @return True if resource is managed, false if released
     */
    bool is_valid() const noexcept { return !m_released; }
};

/**
 * @brief RAII wrapper for malloc/free memory management
 */
class MallocGuard {
private:
    void* m_ptr;
    size_t m_size;

public:
    /**
     * @brief Constructor - allocates memory
     * @param size Size in bytes to allocate
     * @throws std::bad_alloc if allocation fails
     */
    explicit MallocGuard(size_t size) : m_size(size) {
        m_ptr = malloc(size);
        if (!m_ptr && size > 0) {
            throw std::bad_alloc();
        }
        if (m_ptr) {
            std::memset(m_ptr, 0, size);
        }
    }

    /**
     * @brief Destructor - automatically frees memory
     */
    ~MallocGuard() {
        if (m_ptr) {
            // Securely wipe before freeing
            SecureZeroMemory(m_ptr, m_size);
            free(m_ptr);
        }
    }

    // Non-copyable
    MallocGuard(const MallocGuard&) = delete;
    MallocGuard& operator=(const MallocGuard&) = delete;

    // Movable
    MallocGuard(MallocGuard&& other) noexcept : m_ptr(other.m_ptr), m_size(other.m_size) {
        other.m_ptr = nullptr;
        other.m_size = 0;
    }

    MallocGuard& operator=(MallocGuard&& other) noexcept {
        if (this != &other) {
            if (m_ptr) {
                SecureZeroMemory(m_ptr, m_size);
                free(m_ptr);
            }
            m_ptr = other.m_ptr;
            m_size = other.m_size;
            other.m_ptr = nullptr;
            other.m_size = 0;
        }
        return *this;
    }

    /**
     * @brief Get pointer to allocated memory
     * @return Pointer to memory or nullptr
     */
    void* get() noexcept { return m_ptr; }

    /**
     * @brief Get const pointer to allocated memory
     * @return Const pointer to memory or nullptr
     */
    const void* get() const noexcept { return m_ptr; }

    /**
     * @brief Get typed pointer to allocated memory
     * @return Typed pointer to memory or nullptr
     */
    template<typename T>
    T* get_as() noexcept { return static_cast<T*>(m_ptr); }

    /**
     * @brief Get const typed pointer to allocated memory
     * @return Const typed pointer to memory or nullptr
     */
    template<typename T>
    const T* get_as() const noexcept { return static_cast<const T*>(m_ptr); }

    /**
     * @brief Get size of allocated memory
     * @return Size in bytes
     */
    size_t size() const noexcept { return m_size; }

    /**
     * @brief Check if memory is allocated
     * @return True if memory is allocated, false otherwise
     */
    bool is_valid() const noexcept { return m_ptr != nullptr; }

    /**
     * @brief Release memory without freeing (dangerous - use with caution)
     * @return Released pointer
     */
    void* release() noexcept {
        void* ptr = m_ptr;
        m_ptr = nullptr;
        m_size = 0;
        return ptr;
    }
};

/**
 * @brief RAII wrapper for Windows HANDLE management
 */
class HandleGuard {
private:
    HANDLE m_handle;

public:
    /**
     * @brief Constructor with handle
     * @param handle Handle to manage
     */
    explicit HandleGuard(HANDLE handle = INVALID_HANDLE_VALUE) : m_handle(handle) {}

    /**
     * @brief Destructor - automatically closes handle
     */
    ~HandleGuard() {
        if (m_handle != INVALID_HANDLE_VALUE && m_handle != nullptr) {
            CloseHandle(m_handle);
        }
    }

    // Non-copyable
    HandleGuard(const HandleGuard&) = delete;
    HandleGuard& operator=(const HandleGuard&) = delete;

    // Movable
    HandleGuard(HandleGuard&& other) noexcept : m_handle(other.m_handle) {
        other.m_handle = INVALID_HANDLE_VALUE;
    }

    HandleGuard& operator=(HandleGuard&& other) noexcept {
        if (this != &other) {
            if (m_handle != INVALID_HANDLE_VALUE && m_handle != nullptr) {
                CloseHandle(m_handle);
            }
            m_handle = other.m_handle;
            other.m_handle = INVALID_HANDLE_VALUE;
        }
        return *this;
    }

    /**
     * @brief Get the managed handle
     * @return The handle
     */
    HANDLE get() const noexcept { return m_handle; }

    /**
     * @brief Check if handle is valid
     * @return True if handle is valid, false otherwise
     */
    bool is_valid() const noexcept {
        return m_handle != INVALID_HANDLE_VALUE && m_handle != nullptr;
    }

    /**
     * @brief Release handle without closing
     * @return Released handle
     */
    HANDLE release() noexcept {
        HANDLE handle = m_handle;
        m_handle = INVALID_HANDLE_VALUE;
        return handle;
    }

    /**
     * @brief Reset with new handle (closes old one)
     * @param new_handle New handle to manage
     */
    void reset(HANDLE new_handle = INVALID_HANDLE_VALUE) {
        if (m_handle != INVALID_HANDLE_VALUE && m_handle != nullptr) {
            CloseHandle(m_handle);
        }
        m_handle = new_handle;
    }
};

/**
 * @brief Scope guard for automatic cleanup of arbitrary code
 */
class ScopeGuard {
private:
    std::function<void()> m_cleanup;
    bool m_dismissed;

public:
    /**
     * @brief Constructor with cleanup function
     * @param cleanup Function to call on destruction
     */
    explicit ScopeGuard(std::function<void()> cleanup)
        : m_cleanup(cleanup), m_dismissed(false) {
    }

    /**
     * @brief Destructor - calls cleanup if not dismissed
     */
    ~ScopeGuard() {
        if (!m_dismissed && m_cleanup) {
            m_cleanup();
        }
    }

    // Non-copyable and non-movable for safety
    ScopeGuard(const ScopeGuard&) = delete;
    ScopeGuard& operator=(const ScopeGuard&) = delete;
    ScopeGuard(ScopeGuard&&) = delete;
    ScopeGuard& operator=(ScopeGuard&&) = delete;

    /**
     * @brief Dismiss the guard (prevent cleanup)
     */
    void dismiss() noexcept { m_dismissed = true; }
};

// ============================================================================
// UTILITY MACROS FOR EASY USAGE
// ============================================================================

/**
 * @brief Create a scope guard with automatic variable name
 */
#define SCOPE_GUARD(code) \
    ScopeGuard CONCAT(scope_guard_, __LINE__)([&]() { code; })

 /**
  * @brief Create a resource guard for malloc'd memory
  */
#define MALLOC_GUARD(size) MallocGuard(size)

  /**
   * @brief Create a handle guard
   */
#define HANDLE_GUARD(handle) HandleGuard(handle)

   // Helper macro for concatenation
#define CONCAT_IMPL(x, y) x##y
#define CONCAT(x, y) CONCAT_IMPL(x, y)

#endif // __MEMORY_GUARD_H
