﻿  base64.cpp
  Core.cpp
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(263): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(263): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(288): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(288): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(320): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(359): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(359): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(359): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(412): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(412): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(412): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(463): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(479): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(496): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(581): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(581): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(581): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(581): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(625): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(625): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(625): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(625): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(696): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(696): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(724): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(724): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(724): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(724): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(765): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\readconfig.h(765): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\summon.h(402): error C2143: syntax error : missing ',' before ':'
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\summon.h(402): error C2530: 'x' : references must be initialized
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\summon.h(402): error C3531: 'x': a symbol whose type contains 'auto' must have an initializer
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\summon.h(403): error C2143: syntax error : missing ';' before '{'
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\summon.h(404): error C2228: left of '.second' must have class/struct/union
          type is 'int'
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\summon.h(404): error C2228: left of '.first' must have class/struct/union
          type is 'int'
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\summon.h(404): error C2780: 'std::pair<_Ty1,_Ty2> std::_Tree<_Traits>::emplace(_Valty &&)' : expects 1 arguments - 2 provided
          with
          [
              _Ty1=std::_Tree_iterator<std::_Tree_val<std::_Tmap_traits<int,int,std::less<int>,std::allocator<std::pair<const int,int>>,false>>>,
              _Ty2=bool,
              _Traits=std::_Tmap_traits<int,int,std::less<int>,std::allocator<std::pair<const int,int>>,false>
          ]
          c:\program files (x86)\microsoft visual studio 10.0\vc\include\xtree(771) : see declaration of 'std::_Tree<_Traits>::emplace'
          with
          [
              _Traits=std::_Tmap_traits<int,int,std::less<int>,std::allocator<std::pair<const int,int>>,false>
          ]
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\command.h(87): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\command.h(365): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\command.h(399): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
g:\kal\new project\cleaning sources\sources\heth addon 2019\core\command.h(420): warning C4566: character represented by universal-character-name '\u20AC' cannot be represented in the current code page (65001)
  Generating Code...
