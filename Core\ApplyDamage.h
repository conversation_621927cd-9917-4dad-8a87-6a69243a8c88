
//void __fastcall ApplyDamage(void *_this,void *_edx,int Player, int a3, int Damage, int a5, int a6, int a7, int a8)
//{
//	IChar IPlayer((void*)Player);
//	//int afkcd = afkkickcd*60000;
//	//int warningdelay = afkwarningcd*60000;
//	//int warnxcd = afkkickcd-afkwarningcd;
//	if(afkMap[IPlayer.GetPID()].x != IPlayer.GetX() || afkMap[IPlayer.GetPID()].y != IPlayer.GetY()){
//		afkMap[IPlayer.GetPID()].x = IPlayer.GetX();
//		afkMap[IPlayer.GetPID()].y = IPlayer.GetY();
//		afkMap[IPlayer.GetPID()].cd = GetTickCount();
//		afkMap[IPlayer.GetPID()].Warning = false;
//	}
//
//	if(!afkMap[IPlayer.GetPID()].Warning && GetTickCount() > (afkMap[IPlayer.GetPID()].cd + warningdelay) && afkenabled == 1){
//		afkMap[IPlayer.GetPID()].Warning = true;
//			if(afkmovecheck == 1){
//		IPlayer.BoxMsg("Warning, You have been detected as an AFK Player you will be teleported in "+Int2String(warnxcd)+" minute(s) if you are still AFK.");
//			}else if(afkmovecheck == 0){
//		IPlayer.BoxMsg("Warning, You have been detected as an AFK Player");
//			//std::string name = IPlayer.GetName();
//			//std::string msg = ("[Anti AFK] Player ["+name+"] is afk leveling and has been warned");
//			//writeToFile(msg.c_str());
//			afkMap.erase(IPlayer.GetPID());
//			}
//	}else if(afkMap[IPlayer.GetPID()].Warning && GetTickCount() > (afkMap[IPlayer.GetPID()].cd + afkcd) && afkenabled == 1 && afkmovecheck == 1)
//	{
//		IPlayer.BoxMsg("You have been moved for afk leveling.");
//		IPlayer.Teleport(0,267714,242834);
//		//std::string name = IPlayer.GetName();
//		//std::string msg = ("[Anti AFK] Player ["+name+"] is afk leveling and has been moved to fort");
//		//writeToFile(msg.c_str());
//		afkMap.erase(IPlayer.GetPID());
//	}else{
//		CMonsterReal::ApplyDamage(_this,IPlayer.GetOffset(),a3,Damage,a5,a6,a7,a8);
//	}
//
//   
//    
//	
//}
