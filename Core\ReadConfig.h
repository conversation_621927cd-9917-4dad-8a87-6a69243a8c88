
int cbuffid = 0;

//int ScenarioMap = 0, LMSMap = 0;
//int BFMap = 0, BFQuest = 0, BFTime = 0;
//int PLQuest = 0, PLMap = 0, PLRNPC = 0, PLBNPC = 0;

char playermsg[BUFSIZ], cbplayername[BUFSIZ];
int tox = 0, toy=0 , toz = 0;


//int BFRedx1 = 0, BFRedy1 = 0, BFBluex1 = 0 , BFBluey1 = 0;
//int BFRewardItem = 0,BFRewardWinAmount = 0,BFRewardLoseAmount = 0,BFRewardDrawAmount = 0;



int raidBuff = 0,raidLevel = 0,raidMap = 0,raidX = 0,raidY = 0,raidQuest = 0, raidBoss1 = 0,raidBoss2 = 0,raidBoss3 = 0,raidBoss4 = 0,raidRewardIndex = 0,raidRewardAmount = 0,raidRewardBound = 0;
int raidRewardIndex2 = 0,raidRewardAmount2 = 0,raidRewardBound2 = 0;
int raidBoss1X = 0, raidBoss1Y = 0, raidBoss2X = 0 , raidBoss2Y = 0, raidBoss3X = 0 , raidBoss3Y = 0 ,raidBoss4X = 0 , raidBoss4Y = 0 ;
int raidBoss1M = 0 ,raidBoss1MX = 0,raidBoss1MY = 0,raidBoss1MA = 0, raidBoss1MRng = 0;
int raidBoss2M = 0 ,raidBoss2MX = 0,raidBoss2MY = 0,raidBoss2MA = 0, raidBoss2MRng = 0;
int raidBoss3M = 0 ,raidBoss3MX = 0,raidBoss3MY = 0,raidBoss3MA = 0, raidBoss3MRng = 0;
int raidBoss4M = 0 ,raidBoss4MA = 1, skillsDelay = 0, raidBoss4MRng = 0;
int raidManager = 0, raidManagerX = 0, raidManagerY = 0,raidTime = 0, raidMinionsWaveDelay1 = 0, raidMinionsWaveDelay2 = 0, raidMinionsWaveDelay3 = 0;
int waveChecker1 = 0,waveChecker2 = 0,waveChecker3 = 0,waveKillChecker1 = 0,waveKillChecker2 = 0,waveKillChecker3 = 0;
bool waveDead = true;
int waveAmount1 = 0,waveAmount2 = 0,waveAmount3 = 0;
unsigned __int64 raidExp = 1;


//int mutaIndex = 0,mutaBuff = 0, mutaLevel = 0, mutaQuest=0,mutaMap = 0, mutaTeleX = 0, mutaTeleY = 0,mutaTime = 0,mutaSpawnX=0,mutaSpawnY=0,mutaItem=0,
//	mutaBrother=0,mutaBrotherRange = 0,mutaBrotherDmg=0,mutaBrotherHeal=0;
//
//char mutaLinkFX[50] = "", mutaDeathFX[50] = "", mutaHealFX[50] = "", mutaAoEFX[50] = "";
//unsigned __int64 mutaExp = 1;

//int afkenabled = 0,afkkickcd = 0,afkwarningcd = 0,afkmovecheck = 0;

//int pvpAreaQuestID = 0,pvpAreaMap = 0, pvpAreaTeleX = 0,pvpAreaTeleY = 0, pvpAreaBuffId = 0, pvpAreaLevel = 0;

int type = 0, amount = 0;


//int StoneExpFix = 1, ScrollsFix = 1, Meals4ever = 1;
//int PKKillCheck = 1;
//int FishEnabled = 1;
//int LvlLimitNotice = 50, LvlColorNotice = 1;

//int CORCD=0,CORHeal=0,CallsCancel=0,CallsRange = 0,CORHealOther=0;

int KTStimer = 0;

//int FishTeleportQuest=0;
#include "SecureBuffer.h"
#include "MemoryGuard.h"
#include "SimpleDatabase.h"
#include <filesystem>
#include <vector>
#include <string>
#include <sstream>

// For C++14 compatibility, use namespace alias
namespace fs = std::filesystem;

// Secure file reading helper
bool SecureReadConfigFile(const std::string& filename, std::vector<std::string>& lines) {
    try {
        // Validate filename to prevent directory traversal
        fs::path filepath(filename);
        if (filepath.is_absolute() || filepath.string().find("..") != std::string::npos) {
            writeToFile("Security violation: invalid config filename: " + filename);
            return false;
        }

        if (!fs::exists(filepath)) {
            return false;  // File doesn't exist
        }

        std::ifstream file(filename);
        if (!file.is_open()) {
            return false;
        }

        std::string line;
        lines.clear();
        while (std::getline(file, line) && lines.size() < 10000) {  // Limit lines to prevent DoS
            if (line.length() <= 4096) {  // Limit line length
                lines.push_back(line);
            }
        }
        file.close();
        return true;
    }
    catch (const std::exception& e) {
        writeToFile("Error reading config file " + filename + ": " + std::string(e.what()));
        return false;
    }
}

void ReadConfig()
{
	// Initialize random seed for better randomness
	srand((unsigned int)time(NULL));

	// Mautareta
//mutaBuff = GetPrivateProfileIntA("System", "BuffID", 1, "./HethCFG/Mautareta.txt");
//mutaLevel = GetPrivateProfileIntA("System", "Level", 1, "./HethCFG/Mautareta.txt");
//mutaQuest = GetPrivateProfileIntA("System", "Quest", 1, "./HethCFG/Mautareta.txt");
//mutaItem = GetPrivateProfileIntA("System", "RequiredItem", 1, "./HethCFG/Mautareta.txt");
//mutaTime = GetPrivateProfileIntA("System", "Time", 1, "./HethCFG/Mautareta.txt");
//
//mutaMap = GetPrivateProfileIntA("Teleport", "Map", 1, "./HethCFG/Mautareta.txt");
//mutaTeleX = GetPrivateProfileIntA("Teleport", "X", 1, "./HethCFG/Mautareta.txt");
//mutaTeleY = GetPrivateProfileIntA("Teleport", "Y", 1, "./HethCFG/Mautareta.txt"); 
//
//mutaIndex = GetPrivateProfileIntA("Mautareta", "Index", 1, "./HethCFG/Mautareta.txt");
//mutaSpawnX = GetPrivateProfileIntA("Mautareta", "SpawnX", 1, "./HethCFG/Mautareta.txt");
//mutaSpawnY = GetPrivateProfileIntA("Mautareta", "SpawnY", 1, "./HethCFG/Mautareta.txt");
//
//mutaBrother = GetPrivateProfileIntA("Brother", "Index", 1, "./HethCFG/Mautareta.txt");
//mutaBrotherRange = GetPrivateProfileIntA("Brother", "Link-Range", 1, "./HethCFG/Mautareta.txt");
//mutaBrotherDmg = GetPrivateProfileIntA("Brother", "Link-AoEDamage", 1, "./HethCFG/Mautareta.txt");
//mutaBrotherHeal = GetPrivateProfileIntA("Brother", "Link-Heal", 1, "./HethCFG/Mautareta.txt");
//GetPrivateProfileStringA("Brother", "LinkEffect", " ", mutaLinkFX, 50, "./HethCFG/Mautareta.txt");
//GetPrivateProfileStringA("Brother", "DeathEffect", " ", mutaDeathFX, 50, "./HethCFG/Mautareta.txt");
//GetPrivateProfileStringA("Brother", "HealEffect", " ", mutaHealFX, 50, "./HethCFG/Mautareta.txt");
//GetPrivateProfileStringA("Brother", "AoEEffect", " ", mutaAoEFX, 50, "./HethCFG/Mautareta.txt");


// Other
//PKKillCheck = GetPrivateProfileIntA("PkKills", "Notice", 1, "./HethCFG/Other.txt");

//ScrollsFix = GetPrivateProfileIntA("ScrollIconsFix", "Enabled", 1, "./HethCFG/Other.txt");
//StoneExpFix = GetPrivateProfileIntA("StoneExpIcon", "Enabled", 1, "./HethCFG/Other.txt");
//
//LvlLimitNotice =  GetPrivateProfileIntA("NoticeOnLevel", "Above", 50, "./HethCFG/Other.txt");
//LvlColorNotice = GetPrivateProfileIntA("NoticeOnLevel", "Color", 3, "./HethCFG/Other.txt");
//
//CORCD = GetPrivateProfileIntA("CallOfRecovery", "CD", 3, "./HethCFG/Other.txt");
//CORHeal = GetPrivateProfileIntA("CallOfRecovery", "AscHeal", 400, "./HethCFG/Other.txt");
//CORHealOther = GetPrivateProfileIntA("CallOfRecovery", "Heal", 200, "./HethCFG/Other.txt");
//
//
//CallsCancel = GetPrivateProfileIntA("Calls", "Cancel", 1, "./HethCFG/Other.txt");
//CallsRange = GetPrivateProfileIntA("Calls", "Range", 600, "./HethCFG/Other.txt");
//
//Meals4ever = GetPrivateProfileIntA("Meals4ever", "Enabled", 1, "./HethCFG/Other.txt");

// jail in Other
//jailMap = GetPrivateProfileIntA("Jail", "Map", 1, "./HethCFG/Other.txt");
//jailBuff = GetPrivateProfileIntA("Jail", "BuffID", 185, "./HethCFG/Other.txt");
//jailRectX1 = GetPrivateProfileIntA("Jail", "RectX1", 1, "./HethCFG/Other.txt");
//jailRectY1 = GetPrivateProfileIntA("Jail", "RectY1", 1, "./HethCFG/Other.txt");
//jailRectX2 = GetPrivateProfileIntA("Jail", "RectX2", 1, "./HethCFG/Other.txt");
//jailRectY2 = GetPrivateProfileIntA("Jail", "RectY2", 1, "./HethCFG/Other.txt");
//jailBackX = GetPrivateProfileIntA("Jail", "TeleportX", 1, "./HethCFG/Other.txt");
//jailBackY = GetPrivateProfileIntA("Jail", "TeleportY", 1, "./HethCFG/Other.txt");

// pvp sys
//pvpAreaQuestID = GetPrivateProfileIntA("Quest", "ID", 1, "./HethCFG/PvPSystems/PvPArea.txt");
//pvpAreaLevel = GetPrivateProfileIntA("LevelLimit", "Level", 1, "./HethCFG/PvPSystems/PvPArea.txt");
//pvpAreaMap = GetPrivateProfileIntA("Map", "MapID", 1, "./HethCFG/PvPSystems/PvPArea.txt");
//pvpAreaTeleX = GetPrivateProfileIntA("Map", "TeleportX", 1, "./HethCFG/PvPSystems/PvPArea.txt");
//pvpAreaTeleY = GetPrivateProfileIntA("Map", "TeleportY", 1, "./HethCFG/PvPSystems/PvPArea.txt");
//pvpAreaBuffId = GetPrivateProfileIntA("Buff", "BuffID", 1, "./HethCFG/PvPSystems/PvPArea.txt");

// raid sys
raidTime = GetPrivateProfileIntA("Time", "Time", 1, "./HethCFG/Raid.txt");
raidQuest = GetPrivateProfileIntA("Quest", "QuestID", 1, "./HethCFG/Raid.txt");
raidLevel = GetPrivateProfileIntA("LevelLimit", "Level", 1, "./HethCFG/Raid.txt");
raidBuff = GetPrivateProfileIntA("Buff", "ID", 1, "./HethCFG/Raid.txt");

raidMap = GetPrivateProfileIntA("Map", "Map", 1, "./HethCFG/Raid.txt");
raidX = GetPrivateProfileIntA("Map", "TeleportX", 1, "./HethCFG/Raid.txt");
raidY = GetPrivateProfileIntA("Map", "TeleportY", 1, "./HethCFG/Raid.txt");

raidManager = GetPrivateProfileIntA("Manager", "MonsterIndex", 1, "./HethCFG/Raid.txt");
raidManagerX = GetPrivateProfileIntA("Manager", "SpawnX", 1, "./HethCFG/Raid.txt");
raidManagerY = GetPrivateProfileIntA("Manager", "SpawnY", 1, "./HethCFG/Raid.txt");

raidMinionsWaveDelay1 = GetPrivateProfileIntA("AllBosses", "MinionsWave1Delay", 1500, "./HethCFG/Raid.txt");
raidMinionsWaveDelay2 = GetPrivateProfileIntA("AllBosses", "MinionsWave2Delay", 1500, "./HethCFG/Raid.txt");
raidMinionsWaveDelay3 = GetPrivateProfileIntA("AllBosses", "MinionsWave3Delay", 1500, "./HethCFG/Raid.txt");


raidBoss1 = GetPrivateProfileIntA("BossOne", "Index", 1, "./HethCFG/Raid.txt");
raidBoss1X = GetPrivateProfileIntA("BossOne", "SpawnX", 1, "./HethCFG/Raid.txt");
raidBoss1Y = GetPrivateProfileIntA("BossOne", "SpawnY", 1, "./HethCFG/Raid.txt");
raidBoss1M = GetPrivateProfileIntA("BossOne", "MinionsIndex", 1, "./HethCFG/Raid.txt");
raidBoss1MX = GetPrivateProfileIntA("BossOne", "MinionsSpawnX", 1, "./HethCFG/Raid.txt");
raidBoss1MY = GetPrivateProfileIntA("BossOne", "MinionsSpawnY", 1, "./HethCFG/Raid.txt");
raidBoss1MA = GetPrivateProfileIntA("BossOne", "MinionsAmount", 1, "./HethCFG/Raid.txt");
raidBoss1MRng = GetPrivateProfileIntA("BossOne", "MinionsSpawnRange", 300, "./HethCFG/Raid.txt");
raidMinionsWaveDelay1 = GetPrivateProfileIntA("BossOne", "MinionsWavesDelay", 10, "./HethCFG/Raid.txt");
waveAmount1 = GetPrivateProfileIntA("BossOne", "MinionsWavesAmount", 4, "./HethCFG/Raid.txt");


raidBoss2 = GetPrivateProfileIntA("BossTwo", "Index", 1, "./HethCFG/Raid.txt");
raidBoss2X = GetPrivateProfileIntA("BossTwo", "SpawnX", 1, "./HethCFG/Raid.txt");
raidBoss2Y = GetPrivateProfileIntA("BossTwo", "SpawnY", 1, "./HethCFG/Raid.txt");
raidBoss2M = GetPrivateProfileIntA("BossTwo", "MinionsIndex", 1, "./HethCFG/Raid.txt");
raidBoss2MX = GetPrivateProfileIntA("BossTwo", "MinionsSpawnX", 1, "./HethCFG/Raid.txt");
raidBoss2MY = GetPrivateProfileIntA("BossTwo", "MinionsSpawnY", 1, "./HethCFG/Raid.txt");
raidBoss2MA = GetPrivateProfileIntA("BossTwo", "MinionsAmount", 1, "./HethCFG/Raid.txt");
raidBoss2MRng = GetPrivateProfileIntA("BossTwo", "MinionsSpawnRange", 300, "./HethCFG/Raid.txt");
raidMinionsWaveDelay2 = GetPrivateProfileIntA("BossTwo", "MinionsWavesDelay", 10, "./HethCFG/Raid.txt");
waveAmount2 = GetPrivateProfileIntA("BossTwo", "MinionsWavesAmount", 4, "./HethCFG/Raid.txt");

raidBoss3 = GetPrivateProfileIntA("BossThree", "Index", 1, "./HethCFG/Raid.txt");
raidBoss3X = GetPrivateProfileIntA("BossThree", "SpawnX", 1, "./HethCFG/Raid.txt");
raidBoss3Y = GetPrivateProfileIntA("BossThree", "SpawnY", 1, "./HethCFG/Raid.txt");
raidBoss3M = GetPrivateProfileIntA("BossThree", "MinionsIndex", 1, "./HethCFG/Raid.txt");
raidBoss3MX = GetPrivateProfileIntA("BossThree", "MinionsSpawnX", 1, "./HethCFG/Raid.txt");
raidBoss3MY = GetPrivateProfileIntA("BossThree", "MinionsSpawnY", 1, "./HethCFG/Raid.txt");
raidBoss3MA = GetPrivateProfileIntA("BossThree", "MinionsAmount", 1, "./HethCFG/Raid.txt");
raidBoss3MRng = GetPrivateProfileIntA("BossThree", "MinionsSpawnRange", 300, "./HethCFG/Raid.txt");
raidMinionsWaveDelay3 = GetPrivateProfileIntA("BossThree", "MinionsWavesDelay", 10, "./HethCFG/Raid.txt");
waveAmount3 = GetPrivateProfileIntA("BossThree", "MinionsWavesAmount", 4, "./HethCFG/Raid.txt");

raidBoss4 = GetPrivateProfileIntA("BossFour", "Index", 1, "./HethCFG/Raid.txt");
raidBoss4X = GetPrivateProfileIntA("BossFour", "SpawnX", 1, "./HethCFG/Raid.txt");
raidBoss4Y = GetPrivateProfileIntA("BossFour", "SpawnY", 1, "./HethCFG/Raid.txt");
raidBoss4M = GetPrivateProfileIntA("BossFour", "MinionsIndex", 1, "./HethCFG/Raid.txt");
raidBoss4MA = GetPrivateProfileIntA("BossFour", "MinionsAmount", 1, "./HethCFG/Raid.txt");
raidBoss4MRng = GetPrivateProfileIntA("BossFour", "MinionsSpawnRange", 300, "./HethCFG/Raid.txt");
skillsDelay = GetPrivateProfileIntA("BossFour", "SkillsDelay", 10000, "./HethCFG/Raid.txt");

raidRewardIndex = GetPrivateProfileIntA("Reward", "Index", 1, "./HethCFG/Raid.txt");
raidRewardAmount = GetPrivateProfileIntA("Reward", "Amount", 1, "./HethCFG/Raid.txt");
raidRewardBound = GetPrivateProfileIntA("Reward", "Bound", 1, "./HethCFG/Raid.txt");
raidExp = GetPrivateProfileIntA("Reward", "Exp", 1, "./HethCFG/Raid.txt");

raidRewardIndex2 = GetPrivateProfileIntA("Reward2", "Index", 1, "./HethCFG/Raid.txt");
raidRewardAmount2 = GetPrivateProfileIntA("Reward2", "Amount", 1, "./HethCFG/Raid.txt");
raidRewardBound2 = GetPrivateProfileIntA("Reward2", "Bound", 1, "./HethCFG/Raid.txt");



	// battlefield
	//BFTime = GetPrivateProfileIntA("Time", "Time", 3600, "./HethCFG/PvPSystems/GvGBattlefield.txt");
	//BFMap = GetPrivateProfileIntA("Map", "Index", 19, "./HethCFG/PvPSystems/GvGBattlefield.txt");
	//BFQuest = GetPrivateProfileIntA("Quest", "Index", 0, "./HethCFG/PvPSystems/GvGBattlefield.txt");
	//BFBuffT1 = GetPrivateProfileIntA("BuffID", "Team1", 180, "./HethCFG/PvPSystems/GvGBattlefield.txt");
	//BFBuffT2 = GetPrivateProfileIntA("BuffID", "Team2", 179, "./HethCFG/PvPSystems/GvGBattlefield.txt");
	//BFRedx1 = GetPrivateProfileIntA("RedTeleport", "X", 0, "./HethCFG/PvPSystems/GvGBattlefield.txt");
	//BFRedy1 = GetPrivateProfileIntA("RedTeleport", "Y", 0, "./HethCFG/PvPSystems/GvGBattlefield.txt");
	//BFBluex1 = GetPrivateProfileIntA("BlueTeleport", "X", 0, "./HethCFG/PvPSystems/GvGBattlefield.txt");
	//BFBluey1 = GetPrivateProfileIntA("BlueTeleport", "Y", 0, "./HethCFG/PvPSystems/GvGBattlefield.txt");
	//
	//BFRewardItem = GetPrivateProfileIntA("Rewards", "Index", 0, "./HethCFG/PvPSystems/GvGBattlefield.txt");
	//BFRewardWinAmount = GetPrivateProfileIntA("Rewards", "WinAmount", 0, "./HethCFG/PvPSystems/GvGBattlefield.txt");
	//BFRewardLoseAmount = GetPrivateProfileIntA("Rewards", "LoseAmount", 0, "./HethCFG/PvPSystems/GvGBattlefield.txt");



	//afk lvling sys
	//afkenabled = GetPrivateProfileIntA("AFKLeveling", "Enabled", 1, "./HethCFG/AntiAFK.txt");
	//afkmovecheck = GetPrivateProfileIntA("AFKLeveling", "MovePlayer", 1, "./HethCFG/AntiAFK.txt");
	//afkkickcd = GetPrivateProfileIntA("AFKLeveling", "MoveCD", 1, "./HethCFG/AntiAFK.txt");
	//afkwarningcd = GetPrivateProfileIntA("AFKLeveling", "WarnCD", 1, "./HethCFG/AntiAFK.txt");

	
	//fishing 
	////FishEnabled = GetPrivateProfileIntA("System", "Enabled", 1, "./HethCFG/Fishing.txt");
	//FishTeleportQuest = GetPrivateProfileIntA("System", "TeleportQuest", 1, "./HethCFG/Fishing.txt");

	//crossover data from core
	//LMSMap = GetPrivateProfileIntA("Map", "Index", 1, "./Systems/LastManStand.txt");
	//ScenarioMap = GetPrivateProfileIntA("Map", "Index", 100, "./Systems/Destructing.txt");



		FILE *fileNMSkill = fopen("./HethCFG/NewMonsterSkill.txt", "r");
	if (fileNMSkill != NULL)
	{

		char line[BUFSIZ];
		while (fgets(line, sizeof line, fileNMSkill) != NULL)
		{
			int monsterIndex = 0,effectCD=0,effectCD2=1,delay=0,damageMin=1,damageMax=1,effectType=0,aoe=0;
			char effect[BUFSIZ];
			char skillIDsStr[BUFSIZ];


			int monsterSummonIndex = 0,summonhpmin = 0, summonhpmax = 0, summonMinionIndex = 0, summonMinionAmount = 0, smnskilldelay = 0, summonrange = 3,animationSummonID = 0, animationSummonDelay = 0;
			char summoneffect[BUFSIZ];

			// Try parsing with multiple skill IDs first (new format)
			if (sscanf(line, "(Skill (MonsterIndex %d)(AOE %d)(SkillID %[0-9,])(EffectType %d)(Effect '%[a-z | A-Z | 0-9/<>|.,~*;`:!^+%&=?_-�#$�]')(Delay %d)(CD %d)(Damage %d-%d))", &monsterIndex,&aoe,skillIDsStr,&effectType,&effect,&delay,&effectCD,&damageMin,&damageMax) == 9)
			{
				// Validate monster index to prevent array overflow
				if (monsterIndex < 0 || monsterIndex > 9999) {
					continue; // Skip invalid monster index
				}

				NewMonsterSkill[monsterIndex].monsterIndex = monsterIndex;
				NewMonsterSkill[monsterIndex].mSkillIDs.clear(); // Clear any existing skill IDs
				NewMonsterSkill[monsterIndex].CD = (effectCD > 0) ? effectCD : 1; // Ensure minimum CD of 1
				NewMonsterSkill[monsterIndex].EffectType = effectType;
				NewMonsterSkill[monsterIndex].Effect = (strlen(effect) > 0) ? effect : "";
				NewMonsterSkill[monsterIndex].Delay = (delay >= 0) ? delay : 0;
				NewMonsterSkill[monsterIndex].DamageMin = (damageMin > 0) ? damageMin : 1;
				NewMonsterSkill[monsterIndex].DamageMax = (damageMax >= damageMin) ? damageMax : damageMin;
				NewMonsterSkill[monsterIndex].AoE = (aoe >= 0) ? aoe : 0;

				// Parse comma-separated skill IDs
				char *skillToken = strtok(skillIDsStr, ",");
				while (skillToken != NULL && NewMonsterSkill[monsterIndex].mSkillIDs.size() < 10) // Limit to 10 skills max
				{
					int skillID = std::atoi(skillToken);
					if (skillID > 0 && skillID < 999999) // Only add valid skill IDs within reasonable range
					{
						NewMonsterSkill[monsterIndex].mSkillIDs.push_back(skillID);
					}
					skillToken = strtok(NULL, ",");
				}

				// Ensure at least one skill ID was added
				if (NewMonsterSkill[monsterIndex].mSkillIDs.empty()) {
					NewMonsterSkill.erase(monsterIndex); // Remove invalid entry
				}
			}else if (sscanf(line, "(monstersummon (action %d)(animation-delay %d)(monster-index %d)(HP-min %d)(HP-max %d)(Minion-Index %d)(Minion-Amount %d)(Spread-Range %d)(CD %d)(effectname '%[a-z | A-Z | 0-9/<>|.,~*;`:!^+%&=?_-�#$�]'))",&animationSummonID,&animationSummonDelay, &monsterSummonIndex,&summonhpmin, &summonhpmax, &summonMinionIndex, &summonMinionAmount,&summonrange,&smnskilldelay,&summoneffect) == 10)
			{
				MonsterSummonCheak[monsterSummonIndex].animationID = animationSummonID;
				MonsterSummonCheak[monsterSummonIndex].animationDelay = animationSummonDelay;

				MonsterSummonCheak[monsterSummonIndex].monsterSummonIndex = monsterSummonIndex;
				MonsterSummonCheak[monsterSummonIndex].summonhpmin = summonhpmin;
				MonsterSummonCheak[monsterSummonIndex].summonhpmax = summonhpmax;
				MonsterSummonCheak[monsterSummonIndex].summonMinionIndex = summonMinionIndex;
				MonsterSummonCheak[monsterSummonIndex].summonMinionAmount = summonMinionAmount;
				MonsterSummonCheak[monsterSummonIndex].summonrange = summonrange;
				MonsterSummonCheak[monsterSummonIndex].smnskilldelay = smnskilldelay;
				MonsterSummonCheak[monsterSummonIndex].desmnskilldelay = 0;
				MonsterSummonFx[monsterSummonIndex] = summoneffect;

			}



		}
		fclose(fileNMSkill);
	}





	FILE *filebtanker = fopen("./HethCFG/BossTanker.txt", "r");
	if (filebtanker != NULL)
	{
		
		char line[BUFSIZ];
		while (fgets(line, sizeof line, filebtanker) != NULL)
		{
			int bossIndex = 0,effectCD=0;
			char tankerEffect[BUFSIZ];
			char TankerBossName[BUFSIZ];
			if (sscanf(line, "(Boss (Index %d)(EffectCD %d)(BossName '%[a-z | A-Z | 0-9/<>|.,~*;`:!^+%&=?_-�#$�]')(TankerEffect '%[a-z | A-Z | 0-9/<>|.,~*;`:!^+%&=?_-�#$�]')", &bossIndex,&effectCD,&TankerBossName, &tankerEffect) == 4)
			{
				BossTankerFX[bossIndex] = tankerEffect;
				BossTankerBName[bossIndex] = TankerBossName;
				BossTanker[bossIndex].bossIndex = bossIndex;
				BossTanker[bossIndex].cd = 0;
				BossTanker[bossIndex].effectCD = effectCD;
				 
			}
		}
		fclose(filebtanker);
	}



	FILE *filekts = fopen("./HethCFG/KillToSummon.txt", "r");
	if (filekts != NULL)
	{

		char line[BUFSIZ];
		while (fgets(line, sizeof line, filekts) != NULL)
		{
			int minionIndex = 0,minionAmount = 0, bossIndex = 0,bossAmount = 0, spawnX = 0, spawnY = 0, spawnMap = 0,totalKilled=0;
			char bossname[BUFSIZ];
			char minionname[BUFSIZ];
			if (sscanf(line, "(KillToSpawn (MinionIndex %d)(MinionAmount %d)(BossIndex %d)(BossAmount %d)(SpawnCoords %d %d %d)(MinionName '%[a-z | A-Z | 0-9/<>|.,~*;`:!^+%&=?_-�#$�]')(BossName '%[a-z | A-Z | 0-9/<>|.,~*;`:!^+%&=?_-�#$�]'))", &minionIndex, &minionAmount, &bossIndex,&bossAmount,&spawnMap,&spawnX,&spawnY,&minionname,&bossname) == 9)
			{
				KillToSummonBossName[minionIndex] = bossname;
				KillToSummonMinionName[minionIndex] = minionname;
				KillToSummon[minionIndex].minionIndex = minionIndex;
				KillToSummon[minionIndex].minionAmount = minionAmount;
				KillToSummon[minionIndex].bossIndex = bossIndex;
				KillToSummon[minionIndex].bossAmount = bossAmount;
				KillToSummon[minionIndex].spawnX = spawnX;
				KillToSummon[minionIndex].spawnY = spawnY;
				KillToSummon[minionIndex].spawnMap = spawnMap;

				// Use the new simple database system to read totalKilled
				totalKilled = ReadFromSystemDB("killtosummon", minionIndex, 0);
				KillToSummon[minionIndex].totalKilled = totalKilled;
			}
		}
		fclose(filekts);
	}



	// FILE *filebd = fopen("./HethCFG/BossDonations.txt", "r");
	// if (filebd != NULL)
	// {
		
	// 	char line[BUFSIZ];
	// 	while (fgets(line, sizeof line, filebd) != NULL)
	// 	{
	// 		int questIndex = 0,questCheck = 0,itemIndex = 0, itemAmount = 0,monsterIndex = 0,monsterAmount = 0, X = 0, Y = 0, Map = 0,totalCollected=0;
	// 		char bossnotice[BUFSIZ];
	// 		if (sscanf(line, "(boss (questIndex %d)(itemIndex %d)(itemAmount %d)(monsterIndex %d)(monsterAmount %d)(spawnCoords %d %d %d)(bossname '%[a-z | A-Z | 0-9/<>|.,~*;`:!^+%&=?_-�#$�]'))", &questIndex, &itemIndex, &itemAmount,&monsterIndex,&monsterAmount,&Map,&X,&Y,&bossnotice) == 9)
	// 		{
	// 			bossDonationMsg[questIndex] = bossnotice;
	// 			BossDonation[questIndex].questIndex = questIndex;
	// 			BossDonation[questIndex].itemIndex = itemIndex;
	// 			BossDonation[questIndex].itemAmount = itemAmount;
	// 			BossDonation[questIndex].monsterIndex = monsterIndex;
	// 			BossDonation[questIndex].monsterAmount = monsterAmount;
	// 			BossDonation[questIndex].X = X;
	// 			BossDonation[questIndex].Y = Y;
	// 			BossDonation[questIndex].Map = Map;
	// 			std::string mindex = Int2String(monsterIndex);
	// 			std::string path = "./HethCFG/Data/donation_"+mindex+".txt";
				
	// 			totalCollected = GetPrivateProfileIntA(mindex.c_str(), "Amount", 0, path.c_str());
	// 			BossDonation[questIndex].totalCollected = totalCollected;
	// 		}
	// 	}
	// 	fclose(filebd);
	// }








	//FILE *filefish = fopen("./HethCFG/Fishing.txt", "r");
	//if (filefish != NULL)
	//{
	//	char line[BUFSIZ];
	//	while (fgets(line, sizeof line, filefish) != NULL)
	//	{
	//		int bound = 0,level = 0, money = 0;
	//		unsigned __int64 exp = 1;
	//		char Give[BUFSIZ];
	//		char Amount[BUFSIZ];
	//		char Rates[BUFSIZ];
	//		if (sscanf(line, "(Fishing (minlevel %d)(bound %d)(exp %lld)(money %d)(Items '%[a-z | A-Z | 0-9/<>|.,~*;`:!^+%&=?_-�#$�]')(Amounts '%[a-z | A-Z | 0-9/<>|.,~*;`:!'^+%&/()=?_-£#${[]}\/€]')(Rates '%[a-z | A-Z | 0-9/<>|.,~*;`:!'^+%&/()=?_-£#${[]}\/€]'))",&level, &bound, &exp, &money, &Give, &Amount, &Rates) == 7)
	//		{
	//			FishingCheck[1].Items.clear();
	//			FishingCheck[1].Amounts.clear();
	//			FishingCheck[1].Rates.clear();
	//			FishingCheck[1].level = level;
	//			FishingCheck[1].bound = bound;
	//			FishingCheck[1].exp = exp;
	//			FishingCheck[1].money = money;

	//		char *g = strtok(Give,",");
	//		while (g != NULL)
	//		{
	//			if (std::atoi(g))
	//				FishingCheck[1].Items.push_back(std::atoi(g));

	//			g = std::strtok(NULL, ",");
	//		}

	//		char *a = strtok(Amount,",");
	//		while (a != NULL)
	//		{
	//			if (std::atoi(a))
	//				FishingCheck[1].Amounts.push_back(std::atoi(a));

	//			a = std::strtok(NULL, ",");
	//		}

	//			char *r = strtok(Rates,",");
	//		while (r != NULL)
	//		{
	//			if (std::atoi(r))
	//				FishingCheck[1].Rates.push_back(std::atoi(r));

	//			r = std::strtok(NULL, ",");
	//		}

	//		}
	//				

	//	}
	//	fclose(filefish);
	//}



	FILE *filem = fopen("./HethCFG/AutoBattles.txt", "r");
	if (filem != NULL)
	{
		char line[BUFSIZ];
		while (fgets(line, sizeof line, filem) != NULL)
		{
			char day[BUFSIZ],time[BUFSIZ], type[BUFSIZ];
			if (sscanf(line, "(autobattle (day '%[a-z | A-Z | 0-9/<>|.,~*;`:!^+%&=?_-�#$�]')(time '%[a-z | A-Z | 0-9/<>|.,~*;`:!^+%&=?_-�#$�]')(type '%[a-z | A-Z | 0-9/<>|.,~*;`:!^+%&=?_-�#$�]'))", &day, &time, &type) == 3)
			{
				AutoBattleType[time] = type;
				AutoBattleDay[time] = day;
			}
		}
		fclose(filem);
	}



	//	FILE *fileAP = fopen("./HethCFG/AreaProtection.txt", "r");
	//if (fileAP != NULL)
	//{
	//	char line[BUFSIZ];
	//	while (fgets(line, sizeof line, fileAP) != NULL)
	//	{
	//		int apindex = 0,aplevel = 0, apmap = 0, aprectX1 = 0, aprectY1 = 0, aprectX2 = 0, aprectY2 = 0,aptpX = 0, aptpY = 0;
	//		if (sscanf(line, "(area (Index %d)(level %d)(map %d)(rects %d %d %d %d)(tp-back %d %d))",&apindex, &aplevel, &apmap, &aprectX1, &aprectY1, &aprectX2, &aprectY2, &aptpX, &aptpY) == 9)
	//		{
	//			
	//			AreaProtectionCheck[apindex].apindex = apindex;
	//			AreaProtectionCheck[apindex].aplevel = aplevel;
	//			AreaProtectionCheck[apindex].apmap = apmap;
	//			AreaProtectionCheck[apindex].aprectX1 = aprectX1;
	//			AreaProtectionCheck[apindex].aprectY1 = aprectY1;
	//			AreaProtectionCheck[apindex].aprectX2 = aprectX2;
	//			AreaProtectionCheck[apindex].aprectY2 = aprectY2;
	//			AreaProtectionCheck[apindex].aptpX = aptpX;
	//			AreaProtectionCheck[apindex].aptpY = aptpY;

	//		}

	//	}
	//	fclose(fileAP);
	//}


	//	FILE *fileItemBuff = fopen("./HethCFG/ItemUse.txt", "r");
	//if (fileItemBuff != NULL)
	//{
	//	char line[BUFSIZ];
	//	while (fgets(line, sizeof line, fileItemBuff) != NULL)
	//	{
	//		int itemIndex = 0, buffid = 0, buffcd = 0, buffval = 0;
	//		int Index = 0,Html = 0;
	//		int iteminvindex = 0, invexcd = 0;
	//		int indexBlock=0, disabled=0;
	//		if (sscanf(line, "(itembuff (item-index %d)(buff-id %d)(buff-cd %d)(buff-value %d))", &itemIndex, &buffid, &buffcd, &buffval) == 4)
	//		{
	//			ItemBuffCheak[itemIndex].itemIndex = itemIndex;
	//			ItemBuffCheak[itemIndex].buffid = buffid;
	//			ItemBuffCheak[itemIndex].buffcd = buffcd;
	//			ItemBuffCheak[itemIndex].buffval = buffval;

	//		}
	//		if (sscanf(line, "(itemInvExpantion (item-index %d)(cd %d))", &iteminvindex, &invexcd) == 2)
	//		{
	//			ItemInvExCheak[iteminvindex].iteminvindex = iteminvindex;
	//			ItemInvExCheak[iteminvindex].invexcd = invexcd;
	//		}

	//		if (sscanf(line, "(openhtml (item-index %d)(html-page %d))", &Index, &Html) == 2)
	//		{
	//			openHtml[Index].Index = Index;
	//			openHtml[Index].Html = Html;

	//		}

	//		if (sscanf(line, "(blockItem (index %d)(disabled %d))", &indexBlock, &disabled) == 2)
	//		{
	//			ItemBlock[indexBlock].index = indexBlock;
	//			ItemBlock[indexBlock].disabled = disabled;

	//		}



	//	}
	//	fclose(fileItemBuff);
	//}

	// Secure boss drop system configuration reading
	std::vector<std::string> bossLines;
	if (SecureReadConfigFile("./HethCFG/BossDropSystem.txt", bossLines))
	{
		for (const auto& configLine : bossLines)
		{
			if (configLine.empty() || configLine[0] == '#' || configLine[0] == '/') continue;  // Skip comments

			int rngdrops = 0, bossIndex = 0, level = 0, bound = 0, money = 0, shownotice = 0, droprange = 0;
			unsigned __int64 exp = 1;

			// Use secure buffers for parsing
			SecureCharBuffer bossname_buf(256);
			SecureCharBuffer give_buf(1024);
			SecureCharBuffer amount_buf(1024);
			SecureCharBuffer rates_buf(1024);

			int rbossIndex = 0, rlevel = 0, rbound = 0,playerAmount = 0,ovRate = 0, rdroprange = 0;
			char rGive[BUFSIZ];
			char rAmount[BUFSIZ];
			char rRates[BUFSIZ];
			char itemNotice[BUFSIZ];
			if (sscanf(configLine.c_str(), "(droprandomplayer (bossindex %d)(playerAmount %d)(overAllRate %d)(droprange %d)(minlevel %d)(bound %d)(Items '%[0-9/,]')(Amounts '%[0-9/,]')(Rates '%[0-9/,]')(ItemsNotice '%[a-z | A-Z | 0-9/<>|.,~*;`:!^+%&=?_-�#$�]'))", &rbossIndex, &playerAmount,&ovRate,&rdroprange,&rlevel, &rbound, &rGive, &rAmount,&rRates,&itemNotice) == 10)
			{
				rBDSCheckMsg[rbossIndex] = itemNotice;
				rBDSCheck[rbossIndex].Items.clear();
				rBDSCheck[rbossIndex].Amounts.clear();
				rBDSCheck[rbossIndex].Rates.clear();

				rBDSCheck[rbossIndex].rbossIndex = rbossIndex;
				rBDSCheck[rbossIndex].rlevel = rlevel;
				rBDSCheck[rbossIndex].rbound = rbound;
				rBDSCheck[rbossIndex].playerAmount = playerAmount;
				rBDSCheck[rbossIndex].ovRate = ovRate;
				rBDSCheck[rbossIndex].rdroprange = rdroprange;

			char *g = strtok(rGive,",");
			while (g != NULL)
			{
				if (std::atoi(g))
					rBDSCheck[rbossIndex].Items.push_back(std::atoi(g));

				g = std::strtok(NULL, ",");
			}

			char *a = strtok(rAmount,",");
			while (a != NULL)
			{
				if (std::atoi(a))
					rBDSCheck[rbossIndex].Amounts.push_back(std::atoi(a));

				a = std::strtok(NULL, ",");
			}

				char *r = strtok(rRates,",");
			while (r != NULL)
			{
				if (std::atoi(r))
					rBDSCheck[rbossIndex].Rates.push_back(std::atoi(r));

				r = std::strtok(NULL, ",");
			}

			}


			// Use secure parsing with bounds checking
			if (sscanf_s(configLine.c_str(), "(boss (bossindex %d)(bossname '%255[^']')(shownotice %d)(rngdrops %d)(droprange %d)(minlevel %d)(bound %d)(exp %lld)(money %d)(Items '%1023[^']')(Amounts '%1023[^']')(Rates '%1023[^']'))",
				&bossIndex,
				bossname_buf.data(), (unsigned)bossname_buf.size(),
				&shownotice, &rngdrops, &droprange, &level, &bound, &exp, &money,
				give_buf.data(), (unsigned)give_buf.size(),
				amount_buf.data(), (unsigned)amount_buf.size(),
				rates_buf.data(), (unsigned)rates_buf.size()) == 12)
			{
				BDSCheck[bossIndex].Items.clear();
				BDSCheck[bossIndex].Amounts.clear();
				BDSCheck[bossIndex].Rates.clear();

				// Validate boss index to prevent array overflow
				if (bossIndex < 0 || bossIndex > 9999) {
					writeToFile("Invalid boss index in config: " + Int2String(bossIndex));
					continue;
				}

				BossName[bossIndex] = std::string(bossname_buf.data());
				BDSCheck[bossIndex].bossIndex = bossIndex;
				BDSCheck[bossIndex].shownotice = shownotice;
				BDSCheck[bossIndex].rngdrops = rngdrops;
				BDSCheck[bossIndex].level = level;
				BDSCheck[bossIndex].bound = bound;
				BDSCheck[bossIndex].exp = exp;
				BDSCheck[bossIndex].money = money;
				BDSCheck[bossIndex].droprange = droprange;

				// Secure token parsing for items
				std::string give_str(give_buf.data());
				std::string amount_str(amount_buf.data());
				std::string rates_str(rates_buf.data());

				// Parse items with validation
				std::istringstream give_stream(give_str);
				std::string item;
				while (std::getline(give_stream, item, ',') && BDSCheck[bossIndex].Items.size() < 100) {
					int item_id = std::atoi(item.c_str());
					if (item_id > 0 && item_id < 999999) {
						BDSCheck[bossIndex].Items.push_back(item_id);
					}
				}

				// Parse amounts with validation
				std::istringstream amount_stream(amount_str);
				std::string amount;
				while (std::getline(amount_stream, amount, ',') && BDSCheck[bossIndex].Amounts.size() < 100) {
					int amount_val = std::atoi(amount.c_str());
					if (amount_val > 0 && amount_val < 999999) {
						BDSCheck[bossIndex].Amounts.push_back(amount_val);
					}
				}

				// Parse rates with validation
				std::istringstream rates_stream(rates_str);
				std::string rate;
				while (std::getline(rates_stream, rate, ',') && BDSCheck[bossIndex].Rates.size() < 100) {
					int rate_val = std::atoi(rate.c_str());
					if (rate_val > 0 && rate_val <= 10000) {
						BDSCheck[bossIndex].Rates.push_back(rate_val);
					}
				}

			}
					

		}
		// File automatically closed by SecureReadConfigFile
	}


	FILE *filebox = fopen("./HethCFG/Boxes.txt", "r");
	if (filebox != NULL)
	{
		//std::cout << "Reading boxes config..." << std::endl;
		char line[BUFSIZ];
		while (fgets(line, sizeof line, filebox) != NULL)
		{
			int BoxIndex = 0,BoxIsBound = 0;
			int RandomBoxIndex = 0,RandomBoxIsBound = 0;
			int RNGBoxIsBound=0 ,RNGBoxIndex = 0, donotice = 0;
			char RNGBoxName[BUFSIZ];
			char RandomGive[BUFSIZ];
			char RandomAmount[BUFSIZ];
			char Give[BUFSIZ];
			char Amount[BUFSIZ];
			char RNGGive[BUFSIZ];
			char RNGAmount[BUFSIZ];
			char RNGRates[BUFSIZ];
			
			
			if (sscanf(line, "(box (bound %d)(boxindex %d)(Items '%[0-9/,]')(Amounts '%[0-9/,]'))",&BoxIsBound, &BoxIndex, &Give, &Amount) == 4)
			{
				BoxesCheck[BoxIndex].Items.clear();
				BoxesCheck[BoxIndex].Amounts.clear();
				BoxesCheck[BoxIndex].BoxIsBound = BoxIsBound;
				BoxesCheck[BoxIndex].BoxIndex = BoxIndex;
				//std::cout << "BoxIndex: " << BoxIndex << ", BoxIsBound: " << BoxIsBound << std::endl;
			char *g = strtok(Give,",");
			while (g != NULL)
			{
				if (std::atoi(g))
					BoxesCheck[BoxIndex].Items.push_back(std::atoi(g));

				g = std::strtok(NULL, ",");
			}

			char *a = strtok(Amount,",");
			while (a != NULL)
			{
				if (std::atoi(a))
					BoxesCheck[BoxIndex].Amounts.push_back(std::atoi(a));

				a = std::strtok(NULL, ",");
			}
				
	
			}

			if (sscanf(line, "(rngbox (notice '%[a-z | A-Z | 0-9/<>|.,~*;`:!^+%&=?_-�#$�]')(donotice %d)(bound %d)(boxindex %d)(Items '%[0-9/,]')(Amounts '%[0-9/,]')(Rates '%[0-9/,]'))",&RNGBoxName,&donotice,&RNGBoxIsBound, &RNGBoxIndex,&RNGGive, &RNGAmount,&RNGRates) == 7)
			{
				//std::cout << "RNGBoxIndex: " << RNGBoxIndex << ", RNGBoxIsBound: " << RNGBoxIsBound << ", donotice: " << donotice << std::endl;
				RNGBoxesCheck[RNGBoxIndex].Items.clear();
				RNGBoxesCheck[RNGBoxIndex].Amounts.clear();
				RNGBoxesCheck[RNGBoxIndex].Rates.clear();

				RNGBoxesName[RNGBoxIndex] = RNGBoxName;
				RNGBoxesCheck[RNGBoxIndex].RNGBoxIsBound = RNGBoxIsBound;
				RNGBoxesCheck[RNGBoxIndex].RNGBoxIndex = RNGBoxIndex;
				RNGBoxesCheck[RNGBoxIndex].donotice = donotice;

			char *g = strtok(RNGGive,",");
			while (g != NULL)
			{
				if (std::atoi(g))
					RNGBoxesCheck[RNGBoxIndex].Items.push_back(std::atoi(g));

				g = std::strtok(NULL, ",");
			}

			char *a = strtok(RNGAmount,",");
			while (a != NULL)
			{
				if (std::atoi(a))
					RNGBoxesCheck[RNGBoxIndex].Amounts.push_back(std::atoi(a));

				a = std::strtok(NULL, ",");
			}

			char *r = strtok(RNGRates,",");
			while (r != NULL)
			{
				if (std::atoi(r))
					RNGBoxesCheck[RNGBoxIndex].Rates.push_back(std::atoi(r));

				r = std::strtok(NULL, ",");
			}

			}


			if (sscanf(line, "(randombox (bound %d)(boxindex %d)(Items '%[0-9/,]')(Amounts '%[0-9/,]'))",&RandomBoxIsBound, &RandomBoxIndex, &RandomGive, &RandomAmount) == 4)
			{
				//std::cout << "RandomBoxIndex: " << RandomBoxIndex << ", RandomBoxIsBound: " << RandomBoxIsBound << std::endl;
				RandomBoxesCheck[RandomBoxIndex].Items.clear();
				RandomBoxesCheck[RandomBoxIndex].Amounts.clear();
				RandomBoxesCheck[RandomBoxIndex].RandomBoxIsBound = RandomBoxIsBound;
				RandomBoxesCheck[RandomBoxIndex].RandomBoxIndex = RandomBoxIndex;

			char *g = strtok(RandomGive,",");
			while (g != NULL)
			{
				if (std::atoi(g))
					RandomBoxesCheck[RandomBoxIndex].Items.push_back(std::atoi(g));

				g = std::strtok(NULL, ",");
			}

			char *a = strtok(RandomAmount,",");
			while (a != NULL)
			{
				if (std::atoi(a))
					RandomBoxesCheck[RandomBoxIndex].Amounts.push_back(std::atoi(a));

				a = std::strtok(NULL, ",");
			}

	
			}

		}
		fclose(filebox);
	}


}