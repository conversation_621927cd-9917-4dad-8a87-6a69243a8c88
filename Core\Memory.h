/**
 * @file Memory.h
 * @brief Memory manipulation and patching system for game server modifications
 *
 * This file provides low-level memory manipulation capabilities including
 * memory patching, hooking, and runtime code modification. Used extensively
 * for modifying game server behavior through binary patches and function hooks.
 *
 * <AUTHOR> Development Team
 * @version 1.0
 */

#ifndef __MEMORY_H
#define __MEMORY_H

#include <map>
#include <memory>

 // Forward declarations
template<typename T> class SecureBuffer;

class Patch;

/**
 * @brief Interface for memory manipulation and code patching operations
 *
 * The IMemory interface provides comprehensive memory manipulation capabilities
 * including binary patching, function hooking, and runtime code modification.
 * This is the core component for modifying game server behavior at runtime.
 */
class IMemory
{
public:
	/**
	 * @brief Assembly instruction opcodes for memory patching
	 *
	 * Common x86 assembly instruction opcodes used for creating
	 * patches, hooks, and jumps in the game server binary.
	 */
	enum _INSTRUCTIONS
	{
		_I_NOP = 0x90,        ///< No operation instruction
		_I_CALL = 0xe8,       ///< Call instruction (relative)
		_I_JMP = 0xe9,        ///< Jump instruction (relative)
		_I_JMP_SHORT = 0xeb,  ///< Short jump instruction
		_I_JE_SHORT = 0x74,   ///< Jump if equal (short)
		_I_JNZ_SHORT = 0x75,  ///< Jump if not zero (short)
	};

private:
	std::map<void*, Patch*> m_Patches;

public:
	void Copy(void* Source, void* Destination, size_t Size);
	void Fill(unsigned long Destination, unsigned char Fill, size_t Size, bool Recoverable = true);
	void Fill(void* Destination, unsigned char Fill, size_t Size, bool Recoverable = true);
	void Set(unsigned long Destination, unsigned char* Data, size_t Size, bool Recoverable = true);
	void Set(void* Destination, unsigned char* Data, size_t Size, bool Recoverable = true);
	void Set(unsigned long Destination, const char* Data, size_t Size, bool Recoverable = true);
	void Set(void* Destination, const char* Data, size_t Size, bool Recoverable = true);
	void Hook(void* Address, void* Destination, unsigned char Instruction = _I_CALL, size_t Size = 5, bool Recoverable = true);
	void Hook(unsigned long Address, void* Destination, unsigned char Instruction = _I_CALL, size_t Size = 5, bool Recoverable = true);
	void Hook(unsigned long Address[], size_t Count, void* Destination, unsigned char Instruction = _I_CALL, size_t Size = 5, bool Recoverable = true);
	void Hook(void* Address, unsigned long Destination, unsigned char Instruction = _I_CALL, size_t Size = 5, bool Recoverable = true);
	void Hook(unsigned long Address, unsigned long Destination, unsigned char Instruction = _I_CALL, size_t Size = 5, bool Recoverable = true);
	void Hook(unsigned long Address[], size_t Count, unsigned long Destination, unsigned char Instruction = _I_CALL, size_t Size = 5, bool Recoverable = true);
	void HookAPI(void* Address, unsigned long Destination);
	void HookAPI(unsigned long Address, unsigned long Destination);
	void HookAPI(void* Address, void* Destination);
	void HookAPI(unsigned long Address, void* Destination);
	void Restore(void* Address);
	void Restore(unsigned long Address);
	void Restore(unsigned long Address[], size_t Count);
	void Restore(unsigned long Address, void* Destination);
};

/**
 * @brief RAII-compliant patch class for memory modifications
 *
 * This class now uses RAII principles to automatically manage memory
 * and ensure proper cleanup even in exception scenarios.
 */
class Patch
{
private:
	void* m_Address;                                                    ///< Address being patched
	size_t m_Size;                                                     ///< Size of the patch
	unsigned char* m_Original;                                         ///< Legacy pointer (points to RAII-managed memory)
	bool m_Recoverable;                                                ///< Whether patch can be restored
	std::unique_ptr<SecureBuffer<unsigned char>> m_OriginalBuffer;     ///< RAII-managed original data backup

	/**
	 * @brief Private constructor - only IMemory can create patches
	 * @param Address Address to patch
	 * @param Data Patch data
	 * @param Size Size of patch
	 * @param Recoverable Whether patch should be recoverable
	 * @throws std::invalid_argument if parameters are invalid
	 * @throws std::runtime_error if patch creation fails
	 */
	Patch(void* Address, unsigned char* Data, size_t Size, bool Recoverable);

public:
	/**
	 * @brief Destructor - automatically restores original data if recoverable
	 */
	~Patch() noexcept;

	// Non-copyable and non-movable for safety
	Patch(const Patch&) = delete;
	Patch& operator=(const Patch&) = delete;
	Patch(Patch&&) = delete;
	Patch& operator=(Patch&&) = delete;

	friend class IMemory;
};

#endif