
#include "SecureBuffer.h"
#include "MemoryGuard.h"
#include <stdexcept>
#include <memory>

// Constants for better maintainability
namespace SummonConstants {
    constexpr int PROGRESS_SAVE_INTERVAL = 10;  // Save progress every 5%
    constexpr int BOSS_SPAWN_DURATION = 3600000;  // 1 hour in milliseconds
    constexpr int QUEEN_INDEX = 318;
    constexpr int DRAGON_GHOST_TRIGGER = 380;
    constexpr int DRAGON_GHOST_INDEX = 382;
    constexpr int QUEEN_DAMAGE_RANGE = 500;
    constexpr int DELAY_BUFF_ID = 159;
    constexpr int DELAY_BUFF_OFFSET = 2000;
}




// Helper function to check if progress should be saved (reused from GenMonster.h)
bool ShouldSaveProgressSummon(int totalKilled, int minionAmount) {
    if (minionAmount <= 0) return false;

    for (int percent = SummonConstants::PROGRESS_SAVE_INTERVAL; percent < 100;
         percent += SummonConstants::PROGRESS_SAVE_INTERVAL) {
        if (totalKilled == static_cast<int>(minionAmount * percent / 100.0)) {
            return true;
        }
    }
    return false;
}



long cd = 0;
int __cdecl Summon(int Player, int Map, int X, int Y, int Index, int Amount, int SafeZoneCheck, int Delay, int Disappear, int Pet) {
    try {
        void* GetMonster = nullptr;
        int Value = 0, Monster = 0, Argument = 0;
        int GetMap = Undefined::MapCheck(Map);

        if (GetMap) {
            void* MonsterValueCheck = (void*)Undefined::GetMonsterValue(0x2B0u);

            if (MonsterValueCheck) {
                GetMonster = Undefined::MonsterCreate(MonsterValueCheck);
            } else {
                GetMonster = nullptr;
            }

            Monster = (int)GetMonster;
            IChar IMonster((void*)Monster);
            CIOCriticalSection::Enter((void*)0x4E182C);
            Undefined::CreateMonsterValue((void*)0x4E1820, (int)&Argument, (int)&Index);
            int GetCheck = Undefined::Check((int)0x4E1820, (int)&Value);

            if (*(DWORD*)(void*)&Argument == *(DWORD*)GetCheck) {
                CIOCriticalSection::Leave((void*)0x4E182C);
            } else {
                int Check = *(DWORD*)(Undefined::GetValue(&Argument) + 4);

                if (*(DWORD*)(Check + 304)) {
                    // Use RAII for memory management instead of manual new/delete
                    {
                        MallocGuard xyGuard(2 * sizeof(int));
                        int* GetSetXY = static_cast<int*>(xyGuard.get());
                        GetSetXY[0] = X;
                        GetSetXY[1] = Y;

                        *(DWORD*)(Monster + 316) = Map;
                        *(DWORD*)(Monster + 320) = GetMap;
                        *(DWORD*)(Monster + 512) = Index;
                        *(DWORD*)(Monster + 476) = 1;

                        if (Disappear) {
                            MonsterDisappear[(int)IMonster.GetOffset()] = GetTickCount() + Disappear;
                        }

                        CChar::SetXY(Monster, (int)GetSetXY);
                        // Memory automatically freed by MallocGuard destructor
                    }

                    (*(void(__thiscall**)(int, int))(*(DWORD*)Monster + 192))(Monster, Check);

                    if (Delay) {
                        IMonster.Buff(SummonConstants::DELAY_BUFF_ID,
                                    (Delay + SummonConstants::DELAY_BUFF_OFFSET) / 1000, 0);
                        //IMonster.Delay(Delay);
                        IMonster.MobDelay(Delay);
                        IMonster.UnAttack(Delay);
                    }

                    CIOCriticalSection::Leave((void*)0x4E182C);
                    CChar::Lock((void*)Monster);

                    // Use RAII for coordinate memory management
                    {
                        MallocGuard coordGuard(2 * sizeof(int));
                        int* CellMapCoordinate = static_cast<int*>(coordGuard.get());
                        CellMapCoordinate[0] = X >> 5;
                        CellMapCoordinate[1] = Y >> 5;
                        IMonster.MonsterSummonWrite(SafeZoneCheck, Monster, (int)CellMapCoordinate);
                        // Memory automatically freed by MallocGuard destructor
                    }
                } else {
                    CIOCriticalSection::Leave((void*)0x4E182C);
                }
            }
        }

        return Monster;
    }
    catch (const std::exception& e) {
        // Log error but don't crash the game
        return 0;
    }
}
// Helper function to safely spawn bosses (reused from GenMonster.h)
void SafeSpawnBossSummon(int spawnMap, int spawnX, int spawnY, int bossIndex, int bossAmount) {
    try {
        // Validate spawn parameters
        if (spawnMap < 0 || bossIndex <= 0) {
            throw std::invalid_argument("Invalid spawn parameters");
        }

        int actualAmount = (bossAmount <= 1) ? 1 : bossAmount;
        for (int i = 0; i < actualAmount; i++) {
            Summon(0, spawnMap, spawnX, spawnY, bossIndex, 1, 0, 0,
                SummonConstants::BOSS_SPAWN_DURATION, 0);
        }
    }
    catch (const std::exception& e) {
        // Log error but don't crash the game
        // Could add logging here if available
    }
}

void ExecuteMonsterSkill(IChar IPlayer, IChar Object, int DamageMin, int DamageMax) {
	if (!IPlayer.IsValid() || !Object.IsValid() || Object.GetType() != 0) {
		return; // Early return for invalid parameters
	}

	// Validate damage range
	if (DamageMin <= 0 && DamageMax <= 0) {
		return; // No damage to deal
	}

	try {
		int DMG = (DamageMin >= DamageMax) ? DamageMin : CTools::Rate(DamageMin, DamageMax);

		// Level-based damage reduction calculation
		int n = DMG;
		int digits = 0;
		while (n /= 10)
			digits++;

		if (digits > 2)
			digits -= 2;
		else
			digits = 0;

		// Apply level-based damage reduction
		int levelReduction = (DMG > Object.GetLevel()) ? (Object.GetLevel() * (int)pow(10.0, digits)) : DMG;
		DMG = (DMG > levelReduction) ? (DMG - levelReduction) : 1; // Ensure minimum 1 damage

		// Check hit and apply damage
		if (DMG > 0 && IPlayer.CheckHit(Object, DMG) &&
			(*(int(__thiscall **)(int, int, DWORD))(*(DWORD *)IPlayer.GetOffset() + 176))((int)IPlayer.GetOffset(), (int)Object.GetOffset(), 0)) {

			int NormalDamage = 0, DamageArgument = 0, EBDamage = 0, Check = 0, TypeKind = 0, GetType = 0;

			TypeKind = (*(int(__thiscall **)(LONG, void *))(*(DWORD *)Object.GetOffset() + 148))((int)Object.GetOffset(), IPlayer.GetOffset());

			Check = (*(int(__thiscall**)(LONG, void*, unsigned int, int*, int*, int*, DWORD))(*(DWORD*)Object.GetOffset() + 72))((int)Object.GetOffset(), IPlayer.GetOffset(), DMG, &NormalDamage, &DamageArgument, &EBDamage, 0);
			GetType = Check | 2 * DamageArgument | 4 * TypeKind;

			CChar::WriteInSight(IPlayer.GetOffset(), 62, "ddddbd", IPlayer.GetID(), Object.GetID(), NormalDamage, EBDamage, GetType, 0);
		}
	}
	catch (const std::exception& e) {
		// Log error but don't crash the game
		// Could add logging here if available
	}
}

int __fastcall SummonAI(void *Monster, void *edx)
{
	//IChar IMonster(Monster);

	//if (IMonster.IsValid())
	//{
		//if (IMonster.GetMobIndex() == 57)
		//	return 0;

	//}

	return CMonsterMaguniMaster::AI(Monster);
}


// only /summon monster die
int __fastcall SummonDie(int Monster, void * edx, int Arg, int Arg1, int Arg2, int Arg3) {
    try {
        IChar IMonster((void*)Monster);

        // Validate monster object
        if (!IMonster.IsValid()) {
            return CMonsterMaguniMaster::Die(Monster, Arg, Arg1, Arg2, Arg3);
        }

        int mobIndex = IMonster.GetMobIndex();

        // Kill-to-summon system (same logic as GenMonster.h but optimized)
        auto killToSummonIt = KillToSummon.find(mobIndex);
        if (killToSummonIt != KillToSummon.end() &&
            killToSummonIt->second.minionIndex == mobIndex) {

            // Cache the iterator to avoid repeated lookups
            auto& summonData = killToSummonIt->second;
            summonData.totalKilled++;

            int totalKilled = summonData.totalKilled;
            int minionAmount = summonData.minionAmount;

            // Validate amounts to prevent division by zero
            if (minionAmount <= 0) {
                return CMonsterMaguniMaster::Die(Monster, Arg, Arg1, Arg2, Arg3);
            }

            auto bossNameIt = KillToSummonBossName.find(mobIndex);
            if (bossNameIt == KillToSummonBossName.end()) {
                return CMonsterMaguniMaster::Die(Monster, Arg, Arg1, Arg2, Arg3);
            }

            std::string bossname = bossNameIt->second;

            // Save progress at intervals using helper function
            if (ShouldSaveProgressSummon(totalKilled, minionAmount)) {
                saveKilled(summonData.minionIndex, summonData.totalKilled);
            }

            // Check if enough minions killed to spawn boss
            if (totalKilled >= minionAmount) {
                auto minionNameIt = KillToSummonMinionName.find(mobIndex);
                if (minionNameIt != KillToSummonMinionName.end()) {

                    // Create spawn message safely
                    SecureCharBuffer msgBuffer(512);  // Use secure buffer
                    std::string msg = "[" + bossname + "] has spawned after the death of " +
                                    Int2String(minionAmount) + "x [" + minionNameIt->second + "]";

                    if (msg.length() < 500) {  // Bounds check
                        CPlayer::WriteInMap(summonData.spawnMap, 0xFF, "dsd", 247, msg.c_str(), 5);

                        // Spawn boss safely
                        SafeSpawnBossSummon(summonData.spawnMap, summonData.spawnX, summonData.spawnY,
                                          summonData.bossIndex, summonData.bossAmount);
                    }
                }

                // Reset kill count
                summonData.totalKilled = 0;
                saveKilled(summonData.minionIndex, summonData.totalKilled);
            }
        }

        // Raid boss system
        if (Raid::Active == true) {
            if (IMonster.IsBuff(160)) {
                Raid::MinionsKillCount += 1;
            }

            // Check raid boss deaths with constants
            if (raidBoss1 != 0 && mobIndex == raidBoss1) {
                CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "One down !! 3 to go .. HURRY!!! NEXT ROOM !!", 4);
                waveChecker1 = 1;
                waveKillChecker1 = 0;
                Raid::MinionsKillCount = 0;
                Raid::KillCount += 1;
            }

            if (raidBoss2 != 0 && mobIndex == raidBoss2) {
                CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "Two down !! 2 left .. HURRY!!! LETS GO!!", 4);
                waveChecker2 = 1;
                waveKillChecker2 = 0;
                Raid::MinionsKillCount = 0;
                Raid::KillCount += 1;
            }

            if (raidBoss3 != 0 && mobIndex == raidBoss3) {
                CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "THREE DOWN!! 1 left .. this is the hardest, i have faith in you. ", 4);
                waveChecker3 = 1;
                waveKillChecker3 = 0;
                Raid::MinionsKillCount = 0;
                Raid::KillCount += 1;
            }

            if (raidBoss4 != 0 && mobIndex == raidBoss4) {
                Raid::KillCount += 1;
                Raid::MinionsKillCount = 0;
            }
        }



  //check muta bosses
  //if (Muta::Active == true) {
  //  if (IMonster.GetMobIndex() == mutaIndex) {
		// CPlayer::WriteInMap(mutaMap, 0xFF, "dsd", 247, "[Mautareta] You have won naive.. But the huntress is nothing.. without the hunt. You are nothing.. without me.", 1);
  //    Muta::RegisterAmount = 0;
	 // Muta::Dialogue = 0;
  //    MutaRegistration.clear();
  //    Muta::Active = false;

  //  }

  //  if (IMonster.GetMobIndex() == mutaBrother) {
  //    //CPlayer::WriteInMap(mutaMap, 0xFF, "dsd", 247, "[Mautareta Brother] Broth.. Uhhh...", 1);
  //   CPlayer::WriteInMap(mutaMap, 0xFF, "dsd", 247, "[Mautareta] BROTHER!!!!!!!!!!", 8);
	 //IMonster.AddFxToTarget(mutaDeathFX,1,0,0);
  //    Muta::BrotherDead = true;
  //  }


  //}







        // Random player drop system (same logic as GenMonster.h but optimized)
        auto rBDSIt = rBDSCheck.find(mobIndex);
        if (rBDSIt != rBDSCheck.end() && rBDSIt->second.rbossIndex == mobIndex) {

            // Cache the data to avoid repeated lookups
            const auto& rBDSData = rBDSIt->second;
            int Around = IMonster.GetObjectListAround(rBDSData.rdroprange);
            int playerAmountCount = 0;

            // Validate data
            if (rBDSData.playerAmount <= 0 || rBDSData.Items.empty()) {
                return CMonsterMaguniMaster::Die(Monster, Arg, Arg1, Arg2, Arg3);
            }

            while (Around && playerAmountCount < rBDSData.playerAmount) {
                IChar Object((void*)*(DWORD*)Around);

                if (Object.IsValid() && Object.GetType() == 0 &&
                    Object.GetLevel() >= rBDSData.rlevel &&
                    Object.GetMap() == IMonster.GetMap()) {

                    int overallRate = CTools::Rate(1, 1000);

                    if (overallRate > 0 && overallRate <= rBDSData.ovRate) {
                        int itemRate = CTools::Rate(1, 1000);

                        // Process items with bounds checking
                        for (size_t i = 0; i < rBDSData.Items.size() && i < rBDSData.Amounts.size() && i < rBDSData.Rates.size(); i++) {
                            int curRG = rBDSData.Items[i];
                            int curIA = rBDSData.Amounts[i];
                            int curIR = rBDSData.Rates[i];

                            // Validate item data
                            if (curRG <= 0 || curIA <= 0 || curIR <= 0) continue;

                            bool shouldGiveItem = false;
                            if (i == 0) {
                                shouldGiveItem = (itemRate > 0 && itemRate <= curIR);
                            } else {
                                int prevIR = rBDSData.Rates[i-1];
                                shouldGiveItem = (itemRate > prevIR && itemRate <= curIR);
                            }

                            if (shouldGiveItem) {
                                int itemFlags = (rBDSData.rbound == 0) ? 0 : 256;
                                CItem::InsertItem((int)Object.GetOffset(), 27, curRG, itemFlags, curIA, -1);
                                break;  // Only give one item per player
                            }
                        }

                        // Send message if configured (fix logic bug)
                        auto msgIt = rBDSCheckMsg.find(mobIndex);
                        if (msgIt != rBDSCheckMsg.end() &&
                            msgIt->second != "off" && msgIt->second != "0") {

                            SecureCharBuffer msgBuffer(256);
                            std::string playerName = Object.GetName();
                            std::string msg = playerName + " " + msgIt->second;

                            if (msg.length() < 250) {  // Bounds check
                                CPlayer::WriteAll(0xFF, "dsd", 247, msg.c_str(), 4);
                                //DiscordLog(msg);
                            }
                        }

                        playerAmountCount++;
                    }
                }

                Around = CBaseList::Pop((void*)Around);
            }
        }





  // drop sys
  if (BDSCheck.count(IMonster.GetMobIndex()) && BDSCheck.find(IMonster.GetMobIndex()) -> second.bossIndex == IMonster.GetMobIndex()) {
    int Around = IMonster.GetObjectListAround(BDSCheck.find(IMonster.GetMobIndex())->second.droprange);
    IChar Tanker((void * ) IMonster.GetMobTanker());
    std::string monsterName = BossName.find(IMonster.GetMobIndex())->second.c_str();
    std::string tankerName = Tanker.GetName();
    int shownotice = BDSCheck.find(IMonster.GetMobIndex())->second.shownotice;
    int levelMin = BDSCheck.find(IMonster.GetMobIndex())->second.level;
    int isBound = BDSCheck.find(IMonster.GetMobIndex())->second.bound;
    int exp = BDSCheck.find(IMonster.GetMobIndex())->second.exp;
    int money = BDSCheck.find(IMonster.GetMobIndex())->second.money;

	int GetSize = BDSCheck.find(IMonster.GetMobIndex())->second.Items.size();
	

    if (shownotice == 1) {

      std::string msg1 = "Thank you for saving the world from the " + monsterName;
      std::string msg2 = "Rewards has been given for level " + Int2String(levelMin) + "+ warriors.";
      std::string msg3 = "[ " + tankerName + " ] MVP !!";
      CPlayer::WriteAll(0xFF, "dsd", 247, msg1.c_str(), 4);
      CPlayer::WriteAll(0xFF, "dsd", 247, msg2.c_str(), 4);
      CPlayer::WriteAll(0xFF, "dsd", 247, msg3.c_str(), 4);
    }


    while (Around) {
      IChar Object((void * ) * (DWORD * ) Around);
      if (Object.GetType() == 0 && Object.GetLevel() >= levelMin && Object.GetMap() == IMonster.GetMap()) {
        //give exp
        if (exp > 1)
          ( * (int(__cdecl ** )(int, signed int, signed int, unsigned __int64, unsigned __int64))( * (DWORD * ) Object.GetOffset() + 88))((int) Object.GetOffset(), 25, 1, (unsigned __int64) exp, HIDWORD(exp));
		//give money
		if (money != 0)
            CItem::InsertItem((int) Object.GetOffset(), 27, 31, 0, money, -1);

        // give drops
		int Rate = CTools::Rate(1,1000);
		if(BDSCheck.find(IMonster.GetMobIndex())->second.rngdrops == 1){
			
		bool gotItem = false;
		for (int i = 0; i < GetSize; i++)
			{
				int curRG = BDSCheck.find(IMonster.GetMobIndex())->second.Items[i];
				int curIA = BDSCheck.find(IMonster.GetMobIndex())->second.Amounts[i];
				int curIR = BDSCheck.find(IMonster.GetMobIndex())->second.Rates[i];
				
				if(i == 0){
					if(Rate > 0 && Rate <= curIR && curRG != 0 && curIA != 0){
						gotItem = true;

					if(isBound == 0)
						CItem::InsertItem((int)Object.GetOffset(),27,curRG,0,curIA,-1);
					if(isBound == 1)
						CItem::InsertItem((int)Object.GetOffset(),27,curRG,256,curIA,-1);

					
				}
				}else{
					int prevIR = BDSCheck.find(IMonster.GetMobIndex())->second.Rates[i-1];

					if(Rate > prevIR && Rate <= curIR && curRG != 0 && curIA != 0){
						gotItem = true;

						if(isBound == 0)
							CItem::InsertItem((int)Object.GetOffset(),27,curRG,0,curIA,-1);
						if(isBound == 1)
							CItem::InsertItem((int)Object.GetOffset(),27,curRG,256,curIA,-1);
						

					}
				
				}


			}

				if(gotItem == false){
					Object.SystemMessage("Sorry you are unlucky and got no drops, good luck next time.", TEXTCOLOR_RED);
				}
		}else if(BDSCheck.find(IMonster.GetMobIndex())->second.rngdrops == 0){

				for (int i = 0; i < GetSize; i++)
				{
					int curRG = BDSCheck.find(IMonster.GetMobIndex())->second.Items[i];
					int curIA = BDSCheck.find(IMonster.GetMobIndex())->second.Amounts[i];

					if(curRG != 0 && curIA != 0 && isBound == 0)
						CItem::InsertItem((int)Object.GetOffset(),27,curRG,0,curIA,-1);

					if(curRG != 0 && curIA != 0 && isBound == 1)
						CItem::InsertItem((int)Object.GetOffset(),27,curRG,256,curIA,-1);
				
	

				}


			}
      }
      Around = CBaseList::Pop((void * ) Around);
    }
  }

        // Special monster spawns (Queen damage tracking)
        if (mobIndex == SummonConstants::QUEEN_INDEX) {
            int Around = IMonster.GetObjectListAround(SummonConstants::QUEEN_DAMAGE_RANGE);

            while (Around) {
                IChar Object((void*)*(DWORD*)Around);

                if (Object.IsValid() && Object.GetType() == 0 && QueenTopDmg.count(Object.GetID())) {
                    Object.BoxMsg("Your Total Damage Is: " + Int2String(QueenTopDmg.find(Object.GetID())->second));
                }

                Around = CBaseList::Pop((void*)Around);
            }

            // Get top damage dealer safely
            std::map<int, int> QueenTopDmgSorted;
            for (auto const& x : QueenTopDmg) {
                QueenTopDmgSorted.emplace(x.second, x.first);
            }

            if (!QueenTopDmgSorted.empty()) {
                IChar TopDamager((void*)CPlayer::FindPlayer(QueenTopDmgSorted.rbegin()->second));
                if (TopDamager.IsValid()) {
                    SecureCharBuffer msgBuffer(256);
                    std::string msg = "[ The Queen ] top damager is [" + (std::string)TopDamager.GetName() +
                                    "] his total damage is " + Int2String(QueenTopDmg.find(TopDamager.GetID())->second);

                    if (msg.length() < 250) {
                        DiscordLog(msg);
                        CPlayer::WriteAll(0xFF, "dsd", 247, msg, 2);
                    }
                }
            }

            QueenTopDmg.clear();
            QueenTopDmgSorted.clear();
        }

        // Dragon ghost spawn trigger
        if (mobIndex == SummonConstants::DRAGON_GHOST_TRIGGER) {
            Summon(0, IMonster.GetMap(), IMonster.GetX(), IMonster.GetY(),
                   SummonConstants::DRAGON_GHOST_INDEX, 1, 0, 0, SummonConstants::BOSS_SPAWN_DURATION, 0);
            CPlayer::WriteAll(0xFF, "dsd", 247, "The Ghost of Dragon has appeared.", 2);
        }





        return CMonsterMaguniMaster::Die(Monster, Arg, Arg1, Arg2, Arg3);
    }
    catch (const std::exception& e) {
        // Log error but don't crash the game
        return CMonsterMaguniMaster::Die(Monster, Arg, Arg1, Arg2, Arg3);
    }
}

int __fastcall SummonTick(void * Monster, void * edx) {
  IChar IMonster(Monster);


			

  //testing mob vs mob // works but crashes when the other mobs are in the beheadable state
  //if(IMonster.IsValid() && IMonster.GetMobIndex() == 57){

	 // if(!IMonster.GetMobTanker()){
		//int Around = IMonster.GetObjectListAround(10);
		//while(Around)
		//{
		//	IChar OtherMob((void*)*(DWORD*)Around);

		//	if (OtherMob.GetType() == 1 && OtherMob.IsValid() && IMonster.IsValid())
		//	{
		//		if (OtherMob.GetCurHp() > 10 && IMonster.GetCurHp() > 10)
		//		{
		//			if (IMonster.IsValid() && OtherMob.IsValid() && OtherMob.GetMobIndex() != IMonster.GetMobIndex())
		//			{
		//				IMonster.SetMobTarget((int)OtherMob.GetOffset());
		//				OtherMob.SetMobTarget((int)IMonster.GetOffset());
  //            
		//			}
		//		}
		//	}

		//	Around = CBaseList::Pop((void*)Around);
		//	}
		//}

		////if(IMonster.IsValid() && IMonster.GetMobTanker() && (GetTickCount() / 1000) % 2 == 0 && IMonster.GetCurHp() > 10){
		////	IChar Tanker((void*)IMonster.GetMobTanker());
		////	if(Tanker.GetType() == 1 && Tanker.IsValid() && Tanker.GetCurHp() > 10){
		////		IMonster.OktayDamageSingle(Tanker,6,100);
		////		Tanker.OktayDamageSingle(IMonster,6,100);
		////	}
		////	
		////}

  //}










    if (IMonster.IsValid() && IMonster.GetMobTanker() && NewMonsterSkill.count(IMonster.GetMobIndex()) && NewMonsterSkill.find(IMonster.GetMobIndex())->second.monsterIndex == IMonster.GetMobIndex() && (GetTickCount() / 1000) % NewMonsterSkill.find(IMonster.GetMobIndex())->second.CD == 0 )
  {
	  //CChar::WriteInSight(IMonster.Offset, 61, "dbbd", IMonster.GetID(), NewMonsterSkill.find(IMonster.GetMobIndex())->second.mSkillID, NewMonsterSkill.find(IMonster.GetMobIndex())->second.Motion1, IMonster.GetID());
	  //CChar::WriteInSight(IMonster.Offset, 63, "bddbb", NewMonsterSkill.find(IMonster.GetMobIndex())->second.mSkillID, IMonster.GetID(), IMonster.GetID(), NewMonsterSkill.find(IMonster.GetMobIndex())->second.Motion1, NewMonsterSkill.find(IMonster.GetMobIndex())->second.Motion2);

	  // Cache the skill data to avoid repeated lookups
	  const auto& skillData = NewMonsterSkill.find(IMonster.GetMobIndex())->second;

	  // Get a random skill ID from the available skill IDs
	  int SkillID = skillData.getRandomSkillID();
	  if (SkillID <= 0) return CMonsterMaguniMaster::Tick(Monster); // Skip if no valid skill ID

		int DamageMin = skillData.DamageMin;
    int DamageMax = skillData.DamageMax;
    int Delay = skillData.Delay;
    int AoE = skillData.AoE;
    int EffectType = skillData.EffectType;
    const std::string& Effect = skillData.Effect;

		IChar Tanker((void*)IMonster.GetMobTanker());

      if(Delay > 0){
        IMonster.UnAttack(Delay);
      }

      // Apply skill effects
      if(AoE > 0){
        // AoE skill - affect all players in range
        int Around = IMonster.GetObjectListAround(AoE);
        int affectedCount = 0;
        while (Around && affectedCount < 50) { // Limit to prevent infinite loops
          IChar Object((void * ) * (DWORD * ) Around);
          if (Object.IsValid() && Object.GetType() == 0) {
            ExecuteMonsterSkill(IMonster, Object, DamageMin, DamageMax);
            if(EffectType == 0 && !Effect.empty() && Effect != " "){
              Object.AddFxToTarget(Effect.c_str(), 1, 0, 0);
            }
            affectedCount++;
          }
          Around = CBaseList::Pop((void * ) Around);
        }
      } else if (Tanker.IsValid()) {
        // Single target skill - affect tanker only
        ExecuteMonsterSkill(IMonster, Tanker, DamageMin, DamageMax);
      }

      // Apply monster-centered effects
      if(EffectType == 1 && !Effect.empty() && Effect != " "){
        IMonster.AddFxToTarget(Effect.c_str(), 1, 0, 0);
      }

      //show skill animation
      CChar::WriteInSight(IMonster.GetOffset(), 63, "bddbb", SkillID, IMonster.GetID(), IMonster.GetID(), 1, 1);

  	}
  





  //tanker effect
  if (IMonster.IsValid() && BossTanker.count(IMonster.GetMobIndex()) && BossTanker.find(IMonster.GetMobIndex())->second.bossIndex == IMonster.GetMobIndex())
  {
  	IChar Tanker((void*)IMonster.GetMobTanker());
  	if(GetTickCount() >= BossTanker.find(IMonster.GetMobIndex())->second.cd){
  Tanker.AddFxToTarget(BossTankerFX.find(IMonster.GetMobIndex())->second, 1, 0, 0);
  	BossTanker.find(IMonster.GetMobIndex())->second.cd = GetTickCount() + BossTanker.find(IMonster.GetMobIndex())->second.effectCD;
	std::string tankerName = Tanker.GetName();
	if(tankerName != "No Player"){
		std::string mobname = BossTankerBName.find(IMonster.GetMobIndex())->second;
		std::string msg = tankerName + " is tanking " + mobname;
		CPlayer::WriteInMap(IMonster.GetMap(), 0xFF, "dsd", 247, msg.c_str(), 4);
		}
  	}
  }





 //RAID SYSTEM
  if (IMonster.IsValid() && IMonster.GetMobIndex() == raidManager && IMonster.GetCurHp() != IMonster.GetMaxHp() && Raid::Active == true)
    IMonster.IncreaseHp(1000000);

  if (IMonster.IsValid() && Raid::Active == true && IMonster.GetMobIndex() == raidManager) {
    if (Raid::KillCount < 4) {
      //begin raiding
      DWORD Check = (MonsterDisappear.find((int) IMonster.GetOffset()) -> second - GetTickCount()) / 1000;

      if (IMonster.IsValid() && Check == raidTime - 5)
        Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, 1, 0, 1);

      if (IMonster.IsValid() && Check == raidTime - 10) {


        Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, 1, 0, 1);
        CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "[Lisa] Welcome guys,There are 4 bosses!, kill them all in time and i shall reward you.", 4);

      }

      if (IMonster.IsValid() && Check == raidTime - 15) {
        CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "[Lisa] We will start in 15 seconds.", 4);
      }
      if (IMonster.IsValid() && Check == raidTime - 30) {
        Summon(0, raidMap, raidBoss1X, raidBoss1Y, raidBoss1, 1, 0, 0, 3600000, 0);
        CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "[Lisa] The first boss has spawned. Hurry !!", 4);
        int Around = IMonster.GetObjectListAround(1300);

        while (Around) {
          IChar Object((void * ) * (DWORD * ) Around);

          if (Object.GetType() == 0 && Object.IsBuff(raidBuff)) {
            Object.Buff(311, raidTime, 0);
            Object.ScreenTime(raidTime);
          }

          Around = CBaseList::Pop((void * ) Around);
        }

        Raid::ShowTime = raidTime - 30;

        //failed to spawn the first boss
      }

      //WAVES
      //WAVE boss 1
      if (IMonster.IsValid() && Raid::KillCount == 1 && waveChecker1 == 1) {



        if (raidBoss1M != 0) {

        if(waveKillChecker1 != 0){
          if(Raid::MinionsKillCount == raidBoss1MA*waveKillChecker1)
            waveDead = true;
        }

          if ((GetTickCount() / 1000) % raidMinionsWaveDelay1 == 0 && waveKillChecker1 != waveAmount1 && waveDead == true) {
            waveDead = false;
            waveKillChecker1++;
            for (int x = 0; x < raidBoss1MA; x++) {
              int xSummon = Summon(0, raidMap, CTools::Rate(raidBoss1MX, raidBoss1MX + raidBoss1MRng), CTools::Rate(raidBoss1MY, raidBoss1MY + raidBoss1MRng), raidBoss1M, 1, 0, 0, 3600000, 0);
              IChar Minion((void * ) xSummon);
              Minion.Buff(160, 3600, 0);

            }

          }

        } else {
          Raid::MinionsKillCount = raidBoss1MA * waveAmount1;
          waveKillChecker1 = waveAmount1;
        }

        if (Raid::MinionsKillCount == raidBoss1MA*waveAmount1) {
		CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "[Lisa] i can see the second boss. lets move!!", 4);
          Summon(0, raidMap, raidBoss2X, raidBoss2Y, raidBoss2, 1, 0, 0, 3600000, 0);
          waveChecker1 = 0;
          waveKillChecker1 = 0;
          Raid::MinionsKillCount = 0;
          waveDead = true;
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
        }

      }

      //WAVE boss 2
      if (IMonster.IsValid() && Raid::KillCount == 2 && waveChecker2 == 1) {
        if (raidBoss2M != 0) {

         if(waveKillChecker2 != 0){
          if(Raid::MinionsKillCount == raidBoss2MA*waveKillChecker2)
            waveDead = true;
        }
          if ((GetTickCount() / 1000) % raidMinionsWaveDelay2 == 0 && waveKillChecker2 != waveAmount2 && waveDead == true) {
            for (int x = 0; x < raidBoss2MA; x++) {
              int xSummon = Summon(0, raidMap, CTools::Rate(raidBoss2MX, raidBoss2MX + raidBoss2MRng), CTools::Rate(raidBoss2MY, raidBoss2MY + raidBoss2MRng), raidBoss2M, 1, 0, 0, 3600000, 0);
              IChar Minion((void * ) xSummon);
              Minion.Buff(160, 3600, 0);
            }
            waveKillChecker2 += 1;
            waveDead = false;
          }

        } else {
          Raid::MinionsKillCount = raidBoss2MA * waveAmount2;
          waveKillChecker2 = waveAmount2;
        }

        if (Raid::MinionsKillCount == raidBoss2MA * waveAmount2) {
			CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "[Lisa] good job so far!! move to the next boss!!", 4);
          Summon(0, raidMap, raidBoss3X, raidBoss3Y, raidBoss3, 1, 0, 0, 3600000, 0);
          waveChecker2 = 0;
          waveKillChecker2 = 0;
          waveDead = true;
          Raid::MinionsKillCount = 0;
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);

        }

      }

      //WAVE boss 3
      if (IMonster.IsValid() && Raid::KillCount == 3 && waveChecker3 == 1) {
        if (raidBoss3M != 0) {

         if(waveKillChecker3 != 0){
          if(Raid::MinionsKillCount == raidBoss3MA*waveKillChecker3)
            waveDead = true;
        }

          if ((GetTickCount() / 1000) % raidMinionsWaveDelay3 == 0 && waveKillChecker3 != waveAmount3 && waveDead == true) {
            for (int x = 0; x < raidBoss3MA; x++) {
              int xSummon = Summon(0, raidMap, CTools::Rate(raidBoss3MX, raidBoss3MX + raidBoss3MRng), CTools::Rate(raidBoss3MY, raidBoss3MY + raidBoss3MRng), raidBoss3M, 1, 0, 0, 3600000, 0);
              IChar Minion((void * ) xSummon);
              Minion.Buff(160, 3600, 0);

            }
            waveKillChecker3 += 1;
            waveDead = false;

          }

        } else {
          Raid::MinionsKillCount = raidBoss3MA * waveAmount3;
          waveKillChecker3 = waveAmount3;
        }

        if (Raid::MinionsKillCount == raidBoss3MA * waveAmount3) {
          Summon(0, raidMap, raidBoss4X, raidBoss4Y, raidBoss4, 1, 0, 0, 3600000, 0);
          waveChecker3 = 0;
          waveKillChecker3 = 0;
          waveDead = true;
          Raid::MinionsKillCount = 0;
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
		  Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, 0, -120, 0, 1);
      	CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "[Boss] I am the lucid dream, the monster in your nightmares, Fiend of a Thousand Faces, COWER before my true form, ", 1);
        CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "BOW DOWN BEFORE THE GOD OF DEATH!", 1);
        }

      }

      //walking manager
      if (IMonster.IsValid() && Check == raidTime - 34) {
        CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "[Lisa] Yeah, im slow ..", 4);
      }

      if (IMonster.IsValid() && Check == raidTime - 31)
        Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
      if (IMonster.IsValid() && Check == raidTime - 32)
        Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
      if (IMonster.IsValid() && Check == raidTime - 33)
        Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
      if (IMonster.IsValid() && Check == raidTime - 34)
        Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
      if (IMonster.IsValid() && Check == raidTime - 35)
        Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
      if (IMonster.IsValid() && Check == raidTime - 36)
        Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
      if (IMonster.IsValid() && Check == raidTime - 37)
        Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
      if (IMonster.IsValid() && Check == raidTime - 38)
        Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
      if (IMonster.IsValid() && Check == raidTime - 39)
        Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);
	  if (IMonster.IsValid() && Check == raidTime - 39)
        Undefined::MonsterPath( * (void ** )((int) Monster + 320), (int) Monster, -120, 0, 0, 1);


      if (IMonster.IsValid() && Check <= 33) {
        CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "[Lisa] YOU ARE TOO SLOW WHHYYYY!!! The raid has failed...", 4);

        int Around = IMonster.GetObjectListAround(1000);

        while (Around) {
          IChar Object((void * ) * (DWORD * ) Around);

          if (Object.GetType() == 0 && Object.IsBuff(raidBuff)) {
            Object.CloseScreenTime();
            Object.CancelBuff(raidBuff);
          }

          Around = CBaseList::Pop((void * ) Around);
        }

        Raid::KillCount = 0;
        Raid::RegisterAmount = 0;
        RaidRegistration.clear();
        Raid::Active = false;
        Raid::SummonSkillUsed = false;
      }

      //finish and give rewards
    } else {
      CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "[Lisa] Thank you for saving the world from these evil monsters here is your reward...", 4);
      int Around = IMonster.GetObjectListAround(1000);
      //GIVE RAID rewards
      while (Around) {
        IChar Object((void * ) * (DWORD * ) Around);

        if (Object.GetType() == 0 && Object.IsBuff(raidBuff)) {
          if (raidRewardBound == 1) {
            if (raidRewardIndex != 0 && raidRewardAmount != 0)
              CItem::InsertItem((int) Object.GetOffset(), 27, raidRewardIndex, 256, raidRewardAmount, -1);
			
          } else {
            if (raidRewardIndex != 0 && raidRewardAmount != 0)
              CItem::InsertItem((int) Object.GetOffset(), 27, raidRewardIndex, 0, raidRewardAmount, -1);
          }


		   if (raidRewardBound2 == 1) {
            if (raidRewardIndex2 != 0 && raidRewardAmount2 != 0)
              CItem::InsertItem((int) Object.GetOffset(), 27, raidRewardIndex2, 256, raidRewardAmount2, -1);
			
          } else {
            if (raidRewardIndex2 != 0 && raidRewardAmount2 != 0)
              CItem::InsertItem((int) Object.GetOffset(), 27, raidRewardIndex2, 0, raidRewardAmount2, -1);
          }


          ( * (int(__cdecl ** )(int, signed int, signed int, unsigned __int64, unsigned __int64))( * (DWORD * ) Object.GetOffset() + 88))((int) Object.GetOffset(), 25, 1, (unsigned __int64) raidExp, HIDWORD(raidExp));
          Object.CloseScreenTime();
          Object.CancelBuff(raidBuff);
        }

        Around = CBaseList::Pop((void * ) Around);
      }

      
      Raid::KillCount = 0;
      Raid::RegisterAmount = 0;
      RaidRegistration.clear();
      Raid::Active = false;
      Raid::SummonSkillUsed = false;

    }
  }

  if (IMonster.IsValid() && Raid::Active == false && (IMonster.GetMobIndex() == raidManager || IMonster.GetMobIndex() == raidBoss1 || IMonster.GetMobIndex() == raidBoss2 || IMonster.GetMobIndex() == raidBoss3 || IMonster.GetMobIndex() == raidBoss4)) {
    IMonster.MobDelete();
    return 0;
  }

  if (IMonster.IsValid() && Raid::Active == true && IMonster.GetMobIndex() == raidBoss4) {

    int hp95p = IMonster.GetMaxHp() * 0.95;
    int hp70p = IMonster.GetMaxHp() * 0.70;
    int hp10p = IMonster.GetMaxHp() * 0.10;
    // last raidboss debuffs [Hypno], 		// last raidboss debuffs [Poison Cloud] , 		// last raidboss debuffs [No See]
    if (inRange(hp70p, hp95p, IMonster.GetCurHp()) && GetTickCount() > Raid::lastRaidBossSkillDelay) {
      Raid::lastRaidBossSkillDelay = GetTickCount() + skillsDelay;
      IMonster.AddFxToTarget("Doggaebi_21", 1, 0, 0);
      int Around = IMonster.GetObjectListAround(50);
      while (Around) {
        IChar Object((void * ) * (DWORD * ) Around);

        if (Object.GetType() == 0 && Object.GetClass() != 1) {
          Object.Buff(26, 20, 0);
        }

        Around = CBaseList::Pop((void * ) Around);
      }

	      CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "[Lisa] Oh no !! he hypnotized you, i heard he cant hypnotize the mages", 4);
          CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "[Lisa] so thats good? .. i guess", 4);

    } else if (inRange(hp10p, hp70p, IMonster.GetCurHp()) && GetTickCount() > Raid::lastRaidBossSkillDelay) {
      Raid::lastRaidBossSkillDelay = GetTickCount() + skillsDelay;
      IMonster.AddFxToTarget("dave_M604_71", 1, 0, 0);
      int Around = IMonster.GetObjectListAround(50);
      while (Around) {
        IChar Object((void * ) * (DWORD * ) Around);

        if (Object.GetType() == 0) {
          Object.Buff(11, 60, 50);
        }

        Around = CBaseList::Pop((void * ) Around);
      }
	     CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "[Lisa] Im smelling something ...", 4);
         CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "[Lisa] oh nooo !! he poisoned you!! keep healing your yourself ...", 4);

    } else if (inRange(500000, hp10p, IMonster.GetCurHp()) && GetTickCount() > Raid::lastRaidBossSkillDelay) {
      Raid::lastRaidBossSkillDelay = GetTickCount() + skillsDelay;
      IMonster.AddFxToTarget("davi_ef253", 1, 0, 0);
      int Around = IMonster.GetObjectListAround(50);
      while (Around) {
        IChar Object((void * ) * (DWORD * ) Around);

        if (Object.GetType() == 0) {
          Object.Buff(94, 8, 1);
          
        }

        Around = CBaseList::Pop((void * ) Around);
      }
	      CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "[Lisa] WHAAAT!! IM BLINDDDDD!!!!! ...", 4);
          CPlayer::WriteInMap(raidMap, 0xFF, "dsd", 247, "[Lisa] I heard this thing doesn't last long so calm down and keep healing yourself.", 4);
    }

    // last raidboss minions
    if (raidBoss4M != 0 && IMonster.GetCurHp() <= IMonster.GetMaxHp() / 2 && GetTickCount() > Raid::lastRaidBossSkillDelay) {
      Raid::lastRaidBossSkillDelay = GetTickCount() + skillsDelay;
      IMonster.AddFxToTarget("Doggaebi_01", 1, 0, 0);
      for (int x = 0; x < raidBoss4MA; x++)
        Summon(0, raidMap, CTools::Rate(IMonster.GetX(), IMonster.GetX() + raidBoss4MRng), CTools::Rate(IMonster.GetY(), IMonster.GetY() + raidBoss4MRng), raidBoss4M, 1, 1, 0, 3600000, 0);

    }

  }
//RAID SYSTEM DONE



//  if (IMonster.IsValid() && Muta::Active == true && IMonster.GetMobIndex() == mutaIndex) {
//      //begin muta sys
//      DWORD Check = (MonsterDisappear.find((int) IMonster.GetOffset()) -> second - GetTickCount()) / 1000;
//      if (IMonster.IsValid() && Check == mutaTime - 10) {
//        CPlayer::WriteInMap(mutaMap, 0xFF, "dsd", 247, "[Mautareta] New toys? For me? I promise I won't break them this time!", 1);
//      }
//
//	  if(IMonster.IsValid() && Muta::Dialogue == 0 && IMonster.GetCurHp() <= IMonster.GetMaxHp()*0.90){
//		 IChar Tanker((void * ) IMonster.GetMobTanker());
//		 std::string tankerName = Tanker.GetName();
//		 std::string msg = "[Mautareta] " + tankerName + " You call that an attack? Pathatic..";
//        CPlayer::WriteInMap(mutaMap, 0xFF, "dsd", 247, msg.c_str(), 1);
//		Muta::Dialogue = 1;
//      }
//      if(IMonster.IsValid() && Muta::Dialogue == 1 && IMonster.GetCurHp() <= IMonster.GetMaxHp()*0.80){
//        CPlayer::WriteInMap(mutaMap, 0xFF, "dsd", 247, "[Mautareta] Your friends will abandon you!", 1);
//		Muta::Dialogue = 2;
//      }
//      if(IMonster.IsValid() && Muta::Dialogue == 2 && IMonster.GetCurHp() <= IMonster.GetMaxHp()*0.50){
//        CPlayer::WriteInMap(mutaMap, 0xFF, "dsd", 247, "[Mautareta] Brother! Support me !!", 1);
//        Summon(0, mutaMap, IMonster.GetX(), IMonster.GetY(), mutaBrother, 1, 0, 0, mutaTime*1000, 0);
//		Muta::Dialogue = 3;
//      }
//      if(IMonster.IsValid() && Muta::Dialogue == 3 && IMonster.GetCurHp() <= IMonster.GetMaxHp()*0.30){
//        CPlayer::WriteInMap(mutaMap, 0xFF, "dsd", 247, "[Mautareta] NO NO NOOOO! BROTHER TELEPORT TO ME!", 1);
//		Muta::BrotherSeek = true;
//		Muta::Dialogue = 4;
//      }
//      if(IMonster.IsValid() && Muta::Dialogue == 4 && IMonster.GetCurHp() <= IMonster.GetMaxHp()*0.10){
//        CPlayer::WriteInMap(mutaMap, 0xFF, "dsd", 247, "[Mautareta] Mercy? Please!", 1);
//		Muta::Dialogue = 5;
//      }
//
//// check brother range
//	   if(IMonster.IsValid() && !Muta::BrotherDead && IMonster.GetCurHp() <= IMonster.GetMaxHp()*0.50){
//		   int brotherAround = IMonster.GetObjectListAround(mutaBrotherRange);
//		   while (brotherAround) {
//		   IChar Brother((void * ) * (DWORD * ) brotherAround);
//		   if (Brother.GetType() == 1 && Brother.GetMap() == IMonster.GetMap() && Brother.GetMobIndex() == mutaBrother){
//			   if((GetTickCount() / 1000) % 3 == 0){
//				   	IMonster.AddFxToTarget(mutaLinkFX, 1, 0, 0);
//					Brother.AddFxToTarget(mutaLinkFX, 1, 0, 0);
//			   }
//
//			   	if((GetTickCount() / 1000) % 5 == 0){
//				   	IMonster.AddFxToTarget(mutaHealFX, 1, 0, 0);
//					IMonster.IncreaseHp(mutaBrotherHeal);
//			   }
//
//				if((GetTickCount() / 1000) % 8 == 0){
//				   	IMonster.AddFxToTarget(mutaAoEFX, 1, 0, 0);
//					 int Around = IMonster.GetObjectListAround(40);
//					   while (Around) {
//					   IChar Object((void * ) * (DWORD * ) Around);
//					   if (Object.GetType() == 0 && Object.GetMap() == IMonster.GetMap())
//					  IMonster.OktayDamageStorm(Object,mutaBrotherDmg);
//
//					Around = CBaseList::Pop((void * ) Around);
//					}
//
//			   }
//					
//
//		   }
//        brotherAround = CBaseList::Pop((void * ) brotherAround);
//        }
//	   
//
//		   // teleport the brother
//		   if(Muta::BrotherSeek){
//			 int brotherNotAround = IMonster.GetObjectListAround(2000);
//		    while (brotherNotAround) {
//				IChar Brother((void * ) * (DWORD * ) brotherNotAround);
//	   			if(Brother.GetType() == 1 && Brother.GetMap() == IMonster.GetMap() && Brother.GetMobIndex() == mutaBrother){
//				Brother.MobDelete();
//				Summon(0, mutaMap, IMonster.GetX(), IMonster.GetY(), mutaBrother, 1, 0, 0, mutaTime*1000, 0);
//				Muta::BrotherSeek = false;
//
//			}
//				brotherNotAround = CBaseList::Pop((void * ) brotherNotAround);
//			}
//		}
//
//	   }




  // Brother is Dead
    //  if(IMonster.IsValid() && Muta::BrotherDead){
    //    IMonster.AddFxToTarget("davi_ef129_05", 1, 0, 0);
    //    IMonster.IncreaseHp(IMonster.GetMaxHp());
    //    Muta::BrotherDead = false;
    //    Muta::Dialogue = 2;


		  //int Around = IMonster.GetObjectListAround(1500);
		  // while (Around) {
		  // IChar Object((void * ) * (DWORD * ) Around);
		  // if (Object.GetType() == 0 && Object.GetMap() == IMonster.GetMap())
    //      IMonster.OktayDamageStorm(Object,999000);

    //    Around = CBaseList::Pop((void * ) Around);
    //    }



    //  }


  // time is up, all dead.
  //     if (IMonster.IsValid() && Check <= 33) {
		//int Around = IMonster.GetObjectListAround(1000);
		// while (Around) {
		// IChar Object((void * ) * (DWORD * ) Around);
		// if (Object.GetType() == 0)
  //        IMonster.OktayDamageStorm(Object,999000);

  //      Around = CBaseList::Pop((void * ) Around);
  //    }
		//CPlayer::WriteInMap(mutaMap, 0xFF, "dsd", 247, "[Mautareta] You Have Failed, and i will kill you all!!", 1);
		//CPlayer::WriteInMap(mutaMap, 0xFF, "dsd", 247, "[Mautareta] Easy...", 1);
		//Muta::Dialogue = 0;
  //      Muta::RegisterAmount = 0;
  //      MutaRegistration.clear();
  //      Muta::Active = false;
  //    }


  //}





  //if (IMonster.IsValid() && Muta::Active == false && (GetTickCount() / 1000) % 3 == 0 && (IMonster.GetMobIndex() == mutaIndex || IMonster.GetMobIndex() == mutaBrother)) {
  //  IMonster.MobDelete();
  //  return 0;
  //}
  // 
  // 
  // 
  ////monster SKILLS sys
  //if (IMonster.IsValid() && MonsterSkillCheak.count(IMonster.GetMobIndex()) && MonsterSkillCheak.find(IMonster.GetMobIndex()) -> second.monsterSkillIndex == IMonster.GetMobIndex() && GetTickCount() > MonsterSkillCheak[IMonster.GetMobIndex()].deskilldelay) {
  //  int hpPercMin = IMonster.GetMaxHp() * MonsterSkillCheak.find(IMonster.GetMobIndex()) -> second.skillhpmin / 100;
  //  int hpPercMax = IMonster.GetMaxHp() * MonsterSkillCheak.find(IMonster.GetMobIndex()) -> second.skillhpmax / 100;

  //  if (inRange(hpPercMin, hpPercMax, IMonster.GetCurHp())) {
  //    int damageMin = MonsterSkillCheak.find(IMonster.GetMobIndex()) -> second.skilldamagemin;
  //    int damageMax = MonsterSkillCheak.find(IMonster.GetMobIndex()) -> second.skilldamagemax;
  //    int skillRange = MonsterSkillCheak.find(IMonster.GetMobIndex()) -> second.skillrange;
  //    std::string monsterFX = MonsterSkillFx[IMonster.GetMobIndex()];
  //    MonsterSkillCheak[IMonster.GetMobIndex()].deskilldelay = GetTickCount() + MonsterSkillCheak[IMonster.GetMobIndex()].mskilldelay;

	 // if(MonsterSkillCheak.find(IMonster.GetMobIndex())->second.animationID != 0){
		//  IChar Tanker((void*)IMonster.GetMobTanker());
		//  IMonster.UnAttack(MonsterSkillCheak.find(IMonster.GetMobIndex())->second.animationDelay);
		//  IMonster.OktayDamageSingle(Tanker,CTools::Rate(damageMin, damageMax),MonsterSkillCheak.find(IMonster.GetMobIndex())->second.animationID);


		// //CChar::WriteInSight(IMonster.Offset, 61, "dbbd", IMonster.GetID(), 5, MonsterSkillCheak.find(IMonster.GetMobIndex())->second.animationID, IMonster.GetID());
	 // }

  //    if (MonsterSkillFx[IMonster.GetMobIndex()] != " " || MonsterSkillFx[IMonster.GetMobIndex()] != "off") {
  //      IMonster.AddFxToTarget(monsterFX.c_str(), 1, 0, 0);
  //    }



	 // if(MonsterSkillCheak.find(IMonster.GetMobIndex())->second.AoE == 1){
		// int Around = IMonster.GetObjectListAround(skillRange);
  //    while (Around) {
  //      IChar Object((void * ) * (DWORD * ) Around);
  //      if (Object.GetType() == 0)
  //        IMonster.OktayDamageStorm(Object, CTools::Rate(damageMin, damageMax));

  //      Around = CBaseList::Pop((void * ) Around);
  //    }
	 // }




  //  }
  //}

  // summon skills sys
  if (IMonster.IsValid() && MonsterSummonCheak.count(IMonster.GetMobIndex()) && MonsterSummonCheak.find(IMonster.GetMobIndex()) -> second.monsterSummonIndex == IMonster.GetMobIndex() && GetTickCount() > MonsterSummonCheak[IMonster.GetMobIndex()].desmnskilldelay) {
    unsigned __int64 hpPercMin = IMonster.GetMaxHp() * MonsterSummonCheak.find(IMonster.GetMobIndex()) -> second.summonhpmin / 100;
    unsigned __int64 hpPercMax = IMonster.GetMaxHp() * MonsterSummonCheak.find(IMonster.GetMobIndex()) -> second.summonhpmax / 100;

    if (inRange(hpPercMin, hpPercMax, IMonster.GetCurHp())) {
      int minionIndex = MonsterSummonCheak.find(IMonster.GetMobIndex()) -> second.summonMinionIndex;
      int minionAmount = MonsterSummonCheak.find(IMonster.GetMobIndex()) -> second.summonMinionAmount;
      int summonRange = MonsterSummonCheak.find(IMonster.GetMobIndex()) -> second.summonrange;
      int curMap = IMonster.GetMap();
      int curX = IMonster.GetX();
      int curY = IMonster.GetY();
      std::string monsterFX = MonsterSummonFx[IMonster.GetMobIndex()];
      MonsterSummonCheak[IMonster.GetMobIndex()].desmnskilldelay = GetTickCount() + MonsterSummonCheak[IMonster.GetMobIndex()].smnskilldelay;

	  if(MonsterSummonCheak.find(IMonster.GetMobIndex())->second.animationID != 0){
		  IChar Tanker((void*)IMonster.GetMobTanker());
		   IMonster.UnAttack(MonsterSummonCheak.find(IMonster.GetMobIndex())->second.animationDelay);
		  IMonster.OktayDamageSingle(Tanker,500,MonsterSummonCheak.find(IMonster.GetMobIndex())->second.animationID);


		 //CChar::WriteInSight(IMonster.Offset, 61, "dbbd", IMonster.GetID(), 5, MonsterSummonCheak.find(IMonster.GetMobIndex())->second.animationID, IMonster.GetID());
	  }

      if (MonsterSummonFx[IMonster.GetMobIndex()] != " " || MonsterSummonFx[IMonster.GetMobIndex()] != "off") {
        IMonster.AddFxToTarget(monsterFX.c_str(), 1, 0, 0);
        
      }

      if (minionIndex != 0) {
        for (int x = 0; x < minionAmount; x++)
          Summon(0, curMap, CTools::Rate(curX, curX + summonRange), CTools::Rate(curY, curY + summonRange), minionIndex, 1, 1, 0, 3600000, 0);
      }
    }
  }

  //monster debuff sys
 /* if (IMonster.IsValid() && MonsterBuffCheak.count(IMonster.GetMobIndex()) && MonsterBuffCheak.find(IMonster.GetMobIndex()) -> second.monsterIndex == IMonster.GetMobIndex() && GetTickCount() > MonsterBuffCheak[IMonster.GetMobIndex()].debuffdelay) {
    int hpPerc = IMonster.GetMaxHp() * MonsterBuffCheak.find(IMonster.GetMobIndex()) -> second.debuffhp / 100;

    if (IMonster.GetCurHp() <= hpPerc) {
      int index = MonsterBuffCheak.find(IMonster.GetMobIndex()) -> second.monsterIndex;
      int debuffid = MonsterBuffCheak.find(IMonster.GetMobIndex()) -> second.debuffid;
      int debuffcd = MonsterBuffCheak.find(IMonster.GetMobIndex()) -> second.debuffcd;
      int debuffval = MonsterBuffCheak.find(IMonster.GetMobIndex()) -> second.debuffval;
      int excludedclass = MonsterBuffCheak.find(IMonster.GetMobIndex()) -> second.excludedclass;
      int debuffrange = MonsterBuffCheak.find(IMonster.GetMobIndex()) -> second.debuffrange;
      MonsterBuffCheak[IMonster.GetMobIndex()].debuffdelay = GetTickCount() + MonsterBuffCheak[IMonster.GetMobIndex()].skilldelay;

	  if(MonsterBuffCheak.find(IMonster.GetMobIndex())->second.animationID != 0){
		  IChar Tanker((void*)IMonster.GetMobTanker());
		   IMonster.UnAttack(MonsterBuffCheak.find(IMonster.GetMobIndex())->second.animationDelay);
		  IMonster.OktayDamageSingle(Tanker,500,MonsterBuffCheak.find(IMonster.GetMobIndex())->second.animationID);
	  }


	   std::string monsterFX = MonsterBuffFx[IMonster.GetMobIndex()];
      if (MonsterBuffFx[IMonster.GetMobIndex()] != " " || MonsterBuffFx[IMonster.GetMobIndex()] != "off") {
        IMonster.AddFxToTarget(monsterFX.c_str(), 1, 0, 0);
      }

      int Around = IMonster.GetObjectListAround(debuffrange);
      while (Around) {
        IChar Object((void * ) * (DWORD * ) Around);

        if (Object.GetType() == 0 && Object.GetClass() != excludedclass) {
          Object.Buff(debuffid, debuffcd, debuffval);
        }

        Around = CBaseList::Pop((void * ) Around);
      }

    }
  }*/

  return CMonsterMaguniMaster::Tick(Monster);
}