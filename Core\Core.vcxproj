﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{1EDFCAE1-9CF5-42F7-9751-66D16C902AAE}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>Core</RootNamespace>
    <ProjectName>Heth Addons</ProjectName>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>NotSet</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(VCInstallDir)include;$(VCInstallDir)atlmfc\include;$(WindowsSdkDir)include;$(FrameworkSDKDir)\includeC:\Program Files (x86)\Microsoft Research\Detours Express 3.0\src;</IncludePath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;CORE_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>Exports.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;CORE_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <ModuleDefinitionFile>Exports.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="ApplyDamage.h" />
    <ClInclude Include="AutoLearn.h" />
    <ClInclude Include="base64.h" />
    <ClInclude Include="CanAttack.h" />
    <ClInclude Include="Command.h" />
    <ClInclude Include="CriticalLock.h" />
    <ClInclude Include="CutDownExp.h" />
    <ClInclude Include="Exports.h" />
    <ClInclude Include="FinalDamage.h" />
    <ClInclude Include="Functions.h" />
    <ClInclude Include="GenMonster.h" />
    <ClInclude Include="IChar.h" />
    <ClInclude Include="IItem.h" />
    <ClInclude Include="Interface.h" />
    <ClInclude Include="IQuest.h" />
    <ClInclude Include="ISkill.h" />
    <ClInclude Include="ItemUse.h" />
    <ClInclude Include="LevelUp.h" />
    <ClInclude Include="Lock.h" />
    <ClInclude Include="Memory.h" />
    <ClInclude Include="MemoryGuard.h" />
    <ClInclude Include="MutexMap.h" />
    <ClInclude Include="Packet.h" />
    <ClInclude Include="PKKill.h" />
    <ClInclude Include="Player.h" />
    <ClInclude Include="Quest.h" />
    <ClInclude Include="ReadConfig.h" />
    <ClInclude Include="SecureBuffer.h" />
    <ClInclude Include="ShadowSlash.h" />
    <ClInclude Include="Start.h" />
    <ClInclude Include="Summon.h" />
    <ClInclude Include="Time.h" />
    <ClInclude Include="Timer.h" />
    <ClInclude Include="Tools.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Exports.def" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="base64.cpp" />
    <ClCompile Include="Core.cpp" />
    <ClCompile Include="IChar.cpp" />
    <ClCompile Include="IItem.cpp" />
    <ClCompile Include="Interface.cpp" />
    <ClCompile Include="IQuest.cpp" />
    <ClCompile Include="ISkill.cpp" />
    <ClCompile Include="Memory.cpp" />
    <ClCompile Include="Tools.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>