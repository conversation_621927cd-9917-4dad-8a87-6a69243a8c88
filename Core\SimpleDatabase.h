/**
 * @file SimpleDatabase.h
 * @brief Simple key-value database system for storing configuration data
 *
 * This file provides a simple database system that stores key-value pairs
 * in text files with .db extension. Designed to replace INI-style configuration
 * files with a more consistent and secure approach.
 *
 * Features:
 * - Simple text-based format: (registration (Key %d)(Value %d))
 * - Integration with SecureBuffer and MemoryGuard for security
 * - Automatic file management and directory creation
 * - Thread-safe operations
 * - Input validation and bounds checking
 *
 * <AUTHOR> Development Team
 * @version 1.0
 */

#ifndef __SIMPLE_DATABASE_H
#define __SIMPLE_DATABASE_H

#include "SecureBuffer.h"
#include "MemoryGuard.h"
#include <filesystem>
#include <fstream>
#include <map>
#include <string>
#include <sstream>
#include <stdexcept>

// For C++14 compatibility
namespace fs = std::filesystem;

/**
 * @brief Exception thrown when database operations fail
 */
class DatabaseException : public std::runtime_error {
public:
    explicit DatabaseException(const std::string& message)
        : std::runtime_error("Database error: " + message) {}
};

/**
 * @brief Simple key-value database class
 *
 * This class provides a simple database system that stores key-value pairs
 * in text files. Each database instance manages one .db file.
 */
class SimpleDatabase {
private:
    std::string m_filename;                    ///< Database filename
    std::map<int, int> m_data;                ///< In-memory key-value storage
    mutable Lock m_lock;                      ///< Thread safety lock
    bool m_auto_save;                         ///< Whether to auto-save on changes
    
    /**
     * @brief Validate filename to prevent security issues
     * @param filename Filename to validate
     * @throws DatabaseException if filename is invalid
     */
    void validate_filename(const std::string& filename) const {
        fs::path filepath(filename);
        if (filepath.is_absolute() || filepath.string().find("..") != std::string::npos) {
            throw DatabaseException("Invalid filename: security violation");
        }
        
        if (filename.empty() || filename.length() > 255) {
            throw DatabaseException("Invalid filename: empty or too long");
        }
    }
    
    /**
     * @brief Ensure directory exists for the database file
     */
    void ensure_directory_exists() const {
        try {
            fs::path filepath(m_filename);
            fs::path dir = filepath.parent_path();
            if (!dir.empty() && !fs::exists(dir)) {
                fs::create_directories(dir);
            }
        }
        catch (const std::exception& e) {
            throw DatabaseException("Failed to create directory: " + std::string(e.what()));
        }
    }

public:
    /**
     * @brief Constructor with filename
     * @param filename Database filename (relative path)
     * @param auto_save Whether to automatically save changes
     */
    explicit SimpleDatabase(const std::string& filename, bool auto_save = true)
        : m_filename(filename), m_auto_save(auto_save) {
        validate_filename(filename);
        load();
    }
    
    /**
     * @brief Constructor for system-specific database
     * @param system_name Name of the system (e.g., "killtosummon")
     * @param auto_save Whether to automatically save changes
     */
    static SimpleDatabase create_for_system(const std::string& system_name, bool auto_save = true) {
        std::string filename = "./HethCFG/Data/" + system_name + ".db";
        return SimpleDatabase(filename, auto_save);
    }
    
    /**
     * @brief Destructor - saves data if auto_save is enabled
     */
    ~SimpleDatabase() {
        if (m_auto_save) {
            try {
                save();
            }
            catch (...) {
                // Silently fail in destructor to prevent exceptions
            }
        }
    }
    
    // Non-copyable but movable
    SimpleDatabase(const SimpleDatabase&) = delete;
    SimpleDatabase& operator=(const SimpleDatabase&) = delete;
    
    SimpleDatabase(SimpleDatabase&& other) noexcept
        : m_filename(std::move(other.m_filename)),
          m_data(std::move(other.m_data)),
          m_auto_save(other.m_auto_save) {
        other.m_auto_save = false; // Prevent double-save
    }
    
    SimpleDatabase& operator=(SimpleDatabase&& other) noexcept {
        if (this != &other) {
            if (m_auto_save) {
                try { save(); } catch (...) {}
            }
            
            m_filename = std::move(other.m_filename);
            m_data = std::move(other.m_data);
            m_auto_save = other.m_auto_save;
            other.m_auto_save = false;
        }
        return *this;
    }
    
    /**
     * @brief Set a key-value pair
     * @param key Key to set
     * @param value Value to set
     */
    void set(int key, int value) {
        MutexMap lock(m_lock);
        
        // Validate input
        if (key < 0 || key > 99999) {
            throw DatabaseException("Key out of range: " + std::to_string(key));
        }
        if (value < 0 || value > 999999) {
            throw DatabaseException("Value out of range: " + std::to_string(value));
        }
        
        m_data[key] = value;
        
        if (m_auto_save) {
            save_internal();
        }
    }
    
    /**
     * @brief Get a value by key
     * @param key Key to get
     * @param default_value Default value if key doesn't exist
     * @return Value associated with key or default_value
     */
    int get(int key, int default_value = 0) const {
        MutexMap lock(m_lock);
        
        auto it = m_data.find(key);
        return (it != m_data.end()) ? it->second : default_value;
    }
    
    /**
     * @brief Check if a key exists
     * @param key Key to check
     * @return True if key exists, false otherwise
     */
    bool has_key(int key) const {
        MutexMap lock(m_lock);
        return m_data.find(key) != m_data.end();
    }
    
    /**
     * @brief Remove a key-value pair
     * @param key Key to remove
     * @return True if key was removed, false if it didn't exist
     */
    bool remove(int key) {
        MutexMap lock(m_lock);
        
        auto it = m_data.find(key);
        if (it != m_data.end()) {
            m_data.erase(it);
            if (m_auto_save) {
                save_internal();
            }
            return true;
        }
        return false;
    }
    
    /**
     * @brief Clear all data
     */
    void clear() {
        MutexMap lock(m_lock);
        m_data.clear();
        
        if (m_auto_save) {
            save_internal();
        }
    }
    
    /**
     * @brief Get number of key-value pairs
     * @return Number of pairs
     */
    size_t size() const {
        MutexMap lock(m_lock);
        return m_data.size();
    }
    
    /**
     * @brief Check if database is empty
     * @return True if empty, false otherwise
     */
    bool empty() const {
        MutexMap lock(m_lock);
        return m_data.empty();
    }

private:
    /**
     * @brief Internal save method (assumes lock is held)
     */
    void save_internal() const {
        try {
            ensure_directory_exists();
            
            std::ofstream file(m_filename, std::ios::trunc | std::ios::out);
            if (!file.is_open()) {
                throw DatabaseException("Failed to open file for writing: " + m_filename);
            }
            
            for (const auto& pair : m_data) {
                file << "(registration (Key " << pair.first << ")(Value " << pair.second << "))\n";
            }
            
            file.close();
        }
        catch (const std::exception& e) {
            throw DatabaseException("Failed to save: " + std::string(e.what()));
        }
    }

public:
    /**
     * @brief Save data to file
     */
    void save() const {
        MutexMap lock(m_lock);
        save_internal();
    }
    
    /**
     * @brief Load data from file
     */
    void load() {
        MutexMap lock(m_lock);
        
        m_data.clear();
        
        std::ifstream file(m_filename);
        if (!file.is_open()) {
            // File doesn't exist yet, that's okay
            return;
        }
        
        SecureCharBuffer line_buffer(1024);
        std::string line;
        
        while (std::getline(file, line)) {
            if (line.empty() || line[0] == '#') {
                continue; // Skip empty lines and comments
            }
            
            int key, value;
            if (sscanf(line.c_str(), "(registration (Key %d)(Value %d))", &key, &value) == 2) {
                // Validate ranges
                if (key >= 0 && key <= 99999 && value >= 0 && value <= 999999) {
                    m_data[key] = value;
                }
            }
        }
        
        file.close();
    }
    
    /**
     * @brief Reload data from file (discards unsaved changes)
     */
    void reload() {
        load();
    }
    
    /**
     * @brief Get filename
     * @return Database filename
     */
    const std::string& get_filename() const {
        return m_filename;
    }
    
    /**
     * @brief Enable or disable auto-save
     * @param enable Whether to enable auto-save
     */
    void set_auto_save(bool enable) {
        MutexMap lock(m_lock);
        m_auto_save = enable;
    }
    
    /**
     * @brief Check if auto-save is enabled
     * @return True if auto-save is enabled
     */
    bool is_auto_save_enabled() const {
        MutexMap lock(m_lock);
        return m_auto_save;
    }
    
    /**
     * @brief Get all keys
     * @return Vector of all keys
     */
    std::vector<int> get_keys() const {
        MutexMap lock(m_lock);
        std::vector<int> keys;
        keys.reserve(m_data.size());
        
        for (const auto& pair : m_data) {
            keys.push_back(pair.first);
        }
        
        return keys;
    }
    
    /**
     * @brief Get all key-value pairs
     * @return Map of all key-value pairs
     */
    std::map<int, int> get_all() const {
        MutexMap lock(m_lock);
        return m_data; // Copy
    }
};

// ============================================================================
// UTILITY FUNCTIONS FOR EASY INTEGRATION
// ============================================================================

/**
 * @brief Helper function to save a key-value pair to a system database
 * @param system_name Name of the system (e.g., "killtosummon", "bossdrop", etc.)
 * @param key Key to save
 * @param value Value to save
 * @return True if successful, false otherwise
 */
inline bool SaveToSystemDB(const std::string& system_name, int key, int value) {
    try {
        SimpleDatabase db = SimpleDatabase::create_for_system(system_name);
        db.set(key, value);
        return true;
    }
    catch (const std::exception&) {
        return false;
    }
}

/**
 * @brief Helper function to read a value from a system database
 * @param system_name Name of the system (e.g., "killtosummon", "bossdrop", etc.)
 * @param key Key to read
 * @param default_value Default value if key doesn't exist
 * @return Value associated with key or default_value
 */
inline int ReadFromSystemDB(const std::string& system_name, int key, int default_value = 0) {
    try {
        SimpleDatabase db = SimpleDatabase::create_for_system(system_name);
        return db.get(key, default_value);
    }
    catch (const std::exception&) {
        return default_value;
    }
}

/**
 * @brief Helper function to check if a key exists in a system database
 * @param system_name Name of the system
 * @param key Key to check
 * @return True if key exists, false otherwise
 */
inline bool KeyExistsInSystemDB(const std::string& system_name, int key) {
    try {
        SimpleDatabase db = SimpleDatabase::create_for_system(system_name);
        return db.has_key(key);
    }
    catch (const std::exception&) {
        return false;
    }
}

/**
 * @brief Helper function to remove a key from a system database
 * @param system_name Name of the system
 * @param key Key to remove
 * @return True if key was removed, false if it didn't exist or error occurred
 */
inline bool RemoveFromSystemDB(const std::string& system_name, int key) {
    try {
        SimpleDatabase db = SimpleDatabase::create_for_system(system_name);
        return db.remove(key);
    }
    catch (const std::exception&) {
        return false;
    }
}

#endif // __SIMPLE_DATABASE_H
