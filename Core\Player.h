void __fastcall MyGameStart(void *Player, void *edx)
{
	IChar IPlayer(Player);

	if (IPlayer.IsOnline())
	{


		CPlayer::GameStart(Player);

		//if(IPlayer.IsBuff(BFBuffT1)){
		//IPlayer.RemoveSetBlue();
		//IPlayer.RemoveSetRed();
		//IPlayer.CloseScenario3_3Score();
		//IPlayer.CancelBuff(BFBuffT1);
		//IPlayer.Teleport(0, 257514, 259273);
		//}

		//if(IPlayer.IsBuff(BFBuffT2)){
		//IPlayer.RemoveSetBlue();
		//IPlayer.RemoveSetRed();
		//IPlayer.CloseScenario3_3Score();
		//IPlayer.CancelBuff(BFBuffT2);
		//IPlayer.Teleport(0, 257514, 259273);
		//}
	



	//if(IPlayer.IsOnline() && IPlayer.IsBuff(30) && StoneExpFix == 1){
	//	IPlayer.SetBuffIcon(-2, -1, 756, 42);
	//}

	//if(IPlayer.IsOnline() && IPlayer.IsBuff(32) && StoneExpFix == 1){
	//	IPlayer.SetBuffIcon(-2, -1, 755, 41);
	//}


	//if(ScrollsFix == 1){
	//	if (IPlayer.IsBuff(51))
	//	{
	//		IPlayer.SetBuffIcon(IPlayer.GetBuffRemain(51)*1000, 0, 919, 60);
	//	}
	//	if (IPlayer.IsBuff(52))
	//	{
	//		IPlayer.SetBuffIcon(IPlayer.GetBuffRemain(52)*1000, 0, 920, 61);
	//	}
	//	if (IPlayer.IsBuff(54))
	//	{
	//		IPlayer.SetBuffIcon(IPlayer.GetBuffRemain(54)*1000, 0, 921, 62);
	//	}
	//	if (IPlayer.IsBuff(55))
	//	{
	//		IPlayer.SetBuffIcon(IPlayer.GetBuffRemain(55)*1000, 0, 922, 63);
	//	}
	//	if (IPlayer.IsBuff(56))
	//	{
	//		IPlayer.SetBuffIcon(IPlayer.GetBuffRemain(56)*1000, 0, 923, 64);
	//	}
	//	if (IPlayer.IsBuff(57))
	//	{
	//		IPlayer.SetBuffIcon(IPlayer.GetBuffRemain(57)*1000, 0, 924, 65);
	//	}
	//	if (IPlayer.IsBuff(60))
	//	{
	//		IPlayer.SetBuffIcon(IPlayer.GetBuffRemain(60)*1000, 0, 926, 67);
	//	}
	//	if (IPlayer.IsBuff(61))
	//	{
	//		IPlayer.SetBuffIcon(IPlayer.GetBuffRemain(61)*1000, 0, 927, 68);
	//	}
	//	if (IPlayer.IsBuff(62))
	//	{
	//		IPlayer.SetBuffIcon(IPlayer.GetBuffRemain(62)*1000, 0, 928, 69);
	//	}
	//	if (IPlayer.IsBuff(63))
	//	{
	//		IPlayer.SetBuffIcon(IPlayer.GetBuffRemain(63)*1000, 0, 929, 70);
	//	}
	//	if (IPlayer.IsBuff(64))
	//	{
	//		IPlayer.SetBuffIcon(IPlayer.GetBuffRemain(64)*1000, 0, 930, 71);
	//	}
	//}


	//if (IPlayer.IsOnline() && IPlayer.IsBuff(jailBuff) && !Jail.count(IPlayer.GetPID())){
	//	Jail[IPlayer.GetPID()] = IPlayer.GetBuffRemain(jailBuff)*1000+GetTickCount();
	//}


	
	// if(IPlayer.IsOnline() && IPlayer.IsBuff(175)){
	// 	IPlayer.SetBuffIcon(IPlayer.GetBuffRemain(175)*1000,0,3645,430);
	// 	IPlayer.Buff(272,IPlayer.GetBuffRemain(175)*1000,0);
    //     IPlayer.Buff(261,IPlayer.GetBuffRemain(175)*1000,0);
    //     IPlayer.IncreaseMaxHp(1450);
	// }
	
	// if(IPlayer.IsOnline() && IPlayer.IsBuff(176)){
    //     IPlayer.SetBuffIcon(IPlayer.GetBuffRemain(176)*1000,0,3601,415);
	// 	IPlayer.Buff(264,IPlayer.GetBuffRemain(176)*1000,0);
    //     IPlayer.Buff(265,IPlayer.GetBuffRemain(176)*1000,0);
    //     IPlayer.IncreaseMaxHp(500);
    //     IPlayer.IncreaseMaxMp(500);
	// }

	// if(IPlayer.IsOnline() && IPlayer.IsBuff(177)){
    //     IPlayer.SetBuffIcon(IPlayer.GetBuffRemain(177)*1000,0,3604,418);
	// 	IPlayer.Buff(266,IPlayer.GetBuffRemain(177)*1000,0);
    //     IPlayer.Buff(267,IPlayer.GetBuffRemain(177)*1000,0);
    //     IPlayer.AddMaxAttack(75);
    //     IPlayer.AddMinAttack(50);
    //     IPlayer.AddEva(10);
	// 	}

	// if(IPlayer.IsOnline() && IPlayer.IsBuff(178)){
	// 	IPlayer.Buff(268,IPlayer.GetBuffRemain(178)*1000,0);
    //     IPlayer.Buff(269,IPlayer.GetBuffRemain(178)*1000,0);
    //     IPlayer.SetBuffIcon(IPlayer.GetBuffRemain(178)*1000,0,3603,417);
    //     IPlayer.AddMaxAttack(100);
	// 	}

	// if(IPlayer.IsOnline() && IPlayer.IsBuff(179)){
	// 	IPlayer.Buff(270,IPlayer.GetBuffRemain(179)*1000,0);
    //     IPlayer.Buff(271,IPlayer.GetBuffRemain(179)*1000,0);
    //     IPlayer.SetBuffIcon(IPlayer.GetBuffRemain(179)*1000,0,3602,416);
    //     IPlayer.AddMinAttack(75);
    //     IPlayer.AddOTP(10);
	// 	}


	}





	
}









//ticks
int __fastcall Tick(void *Player, void *edx)
{
	IChar IPlayer(Player);




	// area protection
 //	if(IPlayer.IsOnline()){
 //	for(int i=1; i<=AreaProtectionCheck.size(); i++){
 //			if(IPlayer.IsOnline() && AreaProtectionCheck.count(i) && IPlayer.GetRectX() > AreaProtectionCheck.find(i)->second.aprectX1 && IPlayer.GetRectX() < AreaProtectionCheck.find(i)->second.aprectX2 && IPlayer.GetRectY() > AreaProtectionCheck.find(i)->second.aprectY2 && IPlayer.GetRectY() < AreaProtectionCheck.find(i)->second.aprectY1){
 //				if(IPlayer.GetLevel() < AreaProtectionCheck.find(i)->second.aplevel || IPlayer.GetMap() != AreaProtectionCheck.find(i)->second.apmap){
	//		IPlayer.BoxMsg("You are low level for this area, just incase your name has been sent to the Admins.");
	//		//std::string name = IPlayer.GetName();
	//		//std::string msg = ("[Area Protection] Player ["+name+"] is low level and has been moved to fort.");
	//		//writeToFile(msg.c_str());
	//		IPlayer.Teleport(0, AreaProtectionCheck.find(i)->second.aptpX, AreaProtectionCheck.find(i)->second.aptpY);
 //				}	
 //			}
 //	 }
 //}


	// call of recovery rework
	//if (IPlayer.IsValid() && IPlayer.IsOnline()  && (GetTickCount() / 1000) % CORCD == 0 && IPlayer.IsBuff(38) && IPlayer.GetCurHp() < IPlayer.GetMaxHp() && !CChar::IsGState((int)IPlayer.GetOffset(),2)){
	//	if(IPlayer.GetClass() == 1 && IPlayer.GetSpecialty() == 23){
	//		IPlayer.IncreaseHp(CORHeal);
	//	}else{
	//		IPlayer.IncreaseHp(CORHealOther);
	//	}
	//	
	//}

	//if (IPlayer.IsValid() && IPlayer.IsOnline()  && (GetTickCount() / 1000) % CORCD == 0 && IPlayer.IsBuff(38) && IPlayer.GetCurHp() < IPlayer.GetMaxHp() && !CChar::IsGState((int)IPlayer.GetOffset(),2)){
	//	if(IPlayer.GetClass() == 1 && IPlayer.GetSpecialty() == 23){
	//		IPlayer.IncreaseHp(CORHeal);
	//	}else{
	//		IPlayer.IncreaseHp(CORHealOther);
	//	}
	//	
	//}




//if (CallsCancel == 1 && IPlayer.IsOnline() && (GetTickCount() / 1000) % 10 == 0 && IPlayer.IsParty() &&
//	IPlayer.GetClass() == 0 && (IPlayer.GetSpecialty() == 11 || IPlayer.GetSpecialty() == 43)){
//
//
//		//def
//		if(IPlayer.IsBuff(28)){
//		void *Party = (void*)CParty::FindParty(IPlayer.GetPartyID());
//			if (Party)
//			{
//				for (int i = CParty::GetPlayerList(Party); i; i = CBaseList::Pop((void*)i))
//				{
//					int Members = *(DWORD*)((void*)i);
//					IChar IMembers((void*)*(DWORD*)((void*)i));
//
//					if (CChar::IsNormal(Members) && IPlayer.IsValid())
//					{
//							
//
//
//						if (CChar::GetRange((int)IPlayer.GetOffset() + 332, Members + 332) >= CallsRange)
//						{
//		
//							if(IPlayer.GetPID() != IMembers.GetPID())
//								IMembers.CancelBuff(28);
//
//							if(IMembers.GetClass() == 0 && (IMembers.GetSpecialty() == 11 || IMembers.GetSpecialty() == 43) && !IMembers.IsBuff(28)){
//								IPlayer.CancelBuff(74);
//							}
//
//
//						
//						
//					}
//				}
//			}
//		}
//		}
//
//		//cor
//		if(IPlayer.IsBuff(38)){
//		void *Party = (void*)CParty::FindParty(IPlayer.GetPartyID());
//			if (Party)
//			{
//				for (int i = CParty::GetPlayerList(Party); i; i = CBaseList::Pop((void*)i))
//				{
//					int Members = *(DWORD*)((void*)i);
//					IChar IMembers((void*)*(DWORD*)((void*)i));
//
//
//					if (CChar::IsNormal(Members) && IPlayer.IsValid())
//					{
//						if (CChar::GetRange((int)IPlayer.GetOffset() + 332, Members + 332) >= CallsRange)
//						{
//
//							if(IPlayer.GetPID() != IMembers.GetPID())
//								IMembers.CancelBuff(38);
//							if(IMembers.GetClass() == 0 && (IMembers.GetSpecialty() == 11 || IMembers.GetSpecialty() == 43) && !IMembers.IsBuff(38)){
//								IPlayer.CancelBuff(73);
//							}
//						
//					}
//				}
//			}
//		}
//		}
//				//OTP
//		if(IPlayer.IsBuff(73)){
//		void *Party = (void*)CParty::FindParty(IPlayer.GetPartyID());
//			if (Party)
//			{
//				for (int i = CParty::GetPlayerList(Party); i; i = CBaseList::Pop((void*)i))
//				{
//					int Members = *(DWORD*)((void*)i);
//					IChar IMembers((void*)*(DWORD*)((void*)i));
//
//					if (CChar::IsNormal(Members) && IPlayer.IsValid())
//					{
//						if (CChar::GetRange((int)IPlayer.GetOffset() + 332, Members + 332) >= CallsRange)
//						{
//							if(IPlayer.GetPID() != IMembers.GetPID())
//								IMembers.CancelBuff(73);
//							if(IMembers.GetClass() == 0 && (IMembers.GetSpecialty() == 11 || IMembers.GetSpecialty() == 43) && !IMembers.IsBuff(73)){
//								IPlayer.CancelBuff(38);
//							}
//
//						}
//					}
//				}
//			}
//		}
//
//	// ATK
//		if(IPlayer.IsBuff(74)){
//		void *Party = (void*)CParty::FindParty(IPlayer.GetPartyID());
//			if (Party)
//			{
//				for (int i = CParty::GetPlayerList(Party); i; i = CBaseList::Pop((void*)i))
//				{
//					int Members = *(DWORD*)((void*)i);
//					IChar IMembers((void*)*(DWORD*)((void*)i));
//
//					if (CChar::IsNormal(Members) && IPlayer.IsValid())
//					{
//						if (CChar::GetRange((int)IPlayer.GetOffset() + 332, Members + 332) >= CallsRange)
//						{
//							if(IPlayer.GetPID() != IMembers.GetPID())
//								IMembers.CancelBuff(74);
//							if(IMembers.GetClass() == 0 && (IMembers.GetSpecialty() == 11 || IMembers.GetSpecialty() == 43) && !IMembers.IsBuff(74)){
//								IPlayer.CancelBuff(28);
//							}
//
//						}
//					}
//				}
//			}
//		}
//
//
//}

	//raid system checker
		if (IPlayer.IsOnline() && Raid::Active == true && IPlayer.IsBuff(raidBuff) && Raid::ShowTime > 10 && Raid::ShowTime && !IPlayer.IsBuff(312))
	{
		IPlayer.Buff(312,Raid::ShowTime,0);
		IPlayer.ScreenTime(Raid::ShowTime);
	}

	if (IPlayer.IsOnline() && Raid::Active == true && RaidRegistration.count(IPlayer.GetPID()) && RaidRegistration.find(IPlayer.GetPID())->second)
	{
		RaidRegistration.erase(IPlayer.GetPID());

		if (IPlayer.IsValid())
		{
			IPlayer.Teleport(raidMap,raidX,raidY);
			IPlayer.Buff(raidBuff,raidTime,0);
		}
	}

	if (IPlayer.IsOnline() && Raid::Active == true && IPlayer.GetMap() != raidMap && IPlayer.IsBuff(raidBuff))
		IPlayer.Teleport(raidMap,raidX,raidY);

	if (IPlayer.IsOnline() && Raid::Active == true && IPlayer.GetMap() == raidMap && !IPlayer.IsBuff(raidBuff))
		IPlayer.Teleport(0, 257514, 259273);

	if (IPlayer.IsOnline() && Raid::Active == false && IPlayer.GetMap() == raidMap && IPlayer.IsBuff(raidBuff)){
		IPlayer.CloseScreenTime();
		IPlayer.CancelBuff(raidBuff);
		IPlayer.CancelBuff(312);

	}


	////mauta system checker
	//	if (IPlayer.IsOnline() && Muta::Active == true && IPlayer.IsBuff(mutaBuff) && Muta::ShowTime > 10 && Muta::ShowTime && !IPlayer.IsBuff(311))
	//{
	//	IPlayer.Buff(311,Muta::ShowTime,0);
	//	IPlayer.ScreenTime(Muta::ShowTime);
	//}

	//if (IPlayer.IsOnline() && Muta::Active == true && MutaRegistration.count(IPlayer.GetPID()) && MutaRegistration.find(IPlayer.GetPID())->second)
	//{
	//	MutaRegistration.erase(IPlayer.GetPID());

	//	if (IPlayer.IsValid())
	//	{

	//		IPlayer.Teleport(mutaMap,mutaTeleX,mutaTeleY);
	//		IPlayer.Buff(mutaBuff,mutaTime,0);
 //           IPlayer.Buff(311, mutaTime, 0);
 //           IPlayer.ScreenTime(mutaTime);
	//	}
	//}

	//if (IPlayer.IsOnline() && Muta::Active == true && IPlayer.GetMap() != mutaMap && IPlayer.IsBuff(mutaBuff))
	//	IPlayer.Teleport(mutaMap,mutaTeleX,mutaTeleY);

	//if (IPlayer.IsOnline() && Muta::Active == true && IPlayer.GetMap() == mutaMap && !IPlayer.IsBuff(mutaBuff))
	//	IPlayer.Teleport(0, 257514, 259273);

	//if (IPlayer.IsOnline() && Muta::Active == false && IPlayer.GetMap() == mutaMap && IPlayer.IsBuff(mutaBuff)){
	//	IPlayer.CloseScreenTime();
	//	IPlayer.CancelBuff(mutaBuff);
	//	IPlayer.CancelBuff(311);

	//}


		// pvp checker
//	if (IPlayer.IsOnline() && IPlayer.GetMap() == pvpAreaMap && (!IPlayer.IsBuff(pvpAreaBuffId) || !IPlayer.IsBuff(104))){
//	   IPlayer.SystemMessage("You are in a free pvp zone.",TEXTCOLOR_RED);
//		if (!IPlayer.IsBuff(104))
//			IPlayer.Buff(104, 86400,0);
//		if(!IPlayer.IsBuff(pvpAreaBuffId))
//			IPlayer.Buff(pvpAreaBuffId,86400,0);
//	}
//	
//	if (IPlayer.IsOnline() && IPlayer.GetMap() != pvpAreaMap && IPlayer.IsBuff(pvpAreaBuffId)){
//		IPlayer.CancelBuff(pvpAreaBuffId);
//		IPlayer.CancelBuff(104);
//	}
//
//
//	// custom fishing sys
//	if (IPlayer.IsOnline() && FishEnabled == 1 && CChar::IsGState((int)Player,32) && GetTickCount() >= *(DWORD *)((int)Player + 1468))
//	{
//		int isBound = FishingCheck.find(1)->second.bound;
//		int money = FishingCheck.find(1)->second.money;
//		int exp = FishingCheck.find(1)->second.exp;
//		int GetSize = FishingCheck.find(1)->second.Items.size();
//		int Rate = CTools::Rate(1,1000);
//		bool gotItem = false;
//		if(IPlayer.GetLevel() >= FishingCheck.find(1)->second.level){
//        //give exp
//        if (exp > 1)
//          ( * (int(__cdecl ** )(int, signed int, signed int, unsigned __int64, unsigned __int64))( * (DWORD * ) IPlayer.GetOffset() + 88))((int) IPlayer.GetOffset(), 25, 1, (unsigned __int64) exp, HIDWORD(exp));
//		//give money
//		if (money != 0)
//            CItem::InsertItem((int) IPlayer.GetOffset(), 27, 31, 0, money, -1);
//			
//		for (int i = 0; i < GetSize; i++)
//			{
//				int curRG = FishingCheck.find(1)->second.Items[i];
//				int curIA = FishingCheck.find(1)->second.Amounts[i];
//				int curIR = FishingCheck.find(1)->second.Rates[i];
//				
//				if(i == 0){
//					if(Rate > 0 && Rate <= curIR && curRG != 0 && curIA != 0){
//
//					if(isBound == 0)
//						CItem::InsertItem((int)IPlayer.GetOffset(),27,curRG,0,curIA,-1);
//					if(isBound == 1)
//						CItem::InsertItem((int)IPlayer.GetOffset(),27,curRG,256,curIA,-1);
//
//					gotItem = true;
//				}
//				}else{
//					int prevIR = FishingCheck.find(1)->second.Rates[i-1];
//
//					if(Rate > prevIR && Rate <= curIR && curRG != 0 && curIA != 0){
//						gotItem = true;
//
//						if(isBound == 0)
//							CItem::InsertItem((int)IPlayer.GetOffset(),27,curRG,0,curIA,-1);
//						if(isBound == 1)
//							CItem::InsertItem((int)IPlayer.GetOffset(),27,curRG,256,curIA,-1);
//						
//
//					}
//				
//				}
//
//
//			}
//}else{
//	gotItem = true;
//	IPlayer.SystemMessage("Sorry you are low level, and cant fish.", TEXTCOLOR_RED);
//}
//
//
//				if(gotItem == false){
//					IPlayer.SystemMessage("You are unlucky and caught nothing, good luck next time.", TEXTCOLOR_RED);
//				}
//
//
//	}



	/*if (IPlayer.IsOnline() && Protect32::Active == false && IPlayer.GetMap() == BFMap && (IPlayer.IsBuff(BFBuffT1) || IPlayer.IsBuff(BFBuffT2)))
	{
		if (Protect32::Winner && IPlayer.GetGID() == Protect32::Winner)
		{
		CItem::InsertItem((int)IPlayer.GetOffset(),27,BFRewardItem,256,BFRewardWinAmount,-1);
			IPlayer.Buff(119,43200,0);
		}else{
			CItem::InsertItem((int)IPlayer.GetOffset(),27,BFRewardItem,256,BFRewardLoseAmount,-1);
			
		}
		IPlayer.RemoveSetBlue();
		IPlayer.RemoveSetRed();
		IPlayer.CloseScenario3_3Score();
		IPlayer.CancelBuff(BFBuffT1);
		IPlayer.CancelBuff(BFBuffT2);
		IPlayer.Teleport(0, 257514, 259273);
		CItem::InsertItem((int)IPlayer.GetOffset(),27,5011,256,1,-1);
	}

	if (IPlayer.IsOnline() && Protect32::Active == true && (GetTickCount() / 1000) % 5 == 0 && IPlayer.GetMap() == BFMap && !IPlayer.IsBuff(BFBuffT1) && !IPlayer.IsBuff(BFBuffT2))
		IPlayer.Teleport(0, 257514, 259273);

	if (IPlayer.IsOnline() && Protect32::Active == true && Protect32::GuildFirst && IPlayer.GetGID() == Protect32::GuildFirst)
	{
		if (IPlayer.IsValid() && !IPlayer.IsBuff(BFBuffT1))
			IPlayer.Buff(BFBuffT1,4000,0);

		if (IPlayer.IsValid() && IPlayer.GetMap() != BFMap)
			IPlayer.Teleport(BFMap,BFBluex1+CTools::Rate(-100,100),BFBluey1+CTools::Rate(-100,100));

	}

	if (IPlayer.IsOnline() && Protect32::Active == true && Protect32::GuildSecond && IPlayer.GetGID() == Protect32::GuildSecond)
	{
		if (IPlayer.IsValid() && !IPlayer.IsBuff(BFBuffT2))
			IPlayer.Buff(BFBuffT2,4000,0);

		if (IPlayer.IsValid() && IPlayer.GetMap() != BFMap)
			IPlayer.Teleport(BFMap,BFRedx1+CTools::Rate(-100,100),BFRedy1+CTools::Rate(-100,100));
	}

	if (IPlayer.IsOnline() && Protect32::Active == true && IPlayer.GetGID() == Protect32::GuildFirst && IPlayer.IsBuff(BFBuffT1))
	{
			IPlayer.SetBlue();

	}

	if (IPlayer.IsOnline() && Protect32::Active == true && IPlayer.GetGID() == Protect32::GuildSecond && IPlayer.IsBuff(BFBuffT2))
	{
			IPlayer.SetRed();

	}*/


	//player dies
	//if (IPlayer.IsOnline() && CChar::IsGState((int)IPlayer.GetOffset(),2) && (GetTickCount() / 1000) % 5 == 0 && IPlayer.IsBuff(BFBuffT1))
	//{
	//	Protect32::BlueScore += 1;	
	//	IPlayer.Teleport(BFMap,BFBluex1+CTools::Rate(-50,50),BFBluey1+CTools::Rate(-50,50));
	//	IPlayer.Revive();
	//}


	//if (IPlayer.IsOnline() && CChar::IsGState((int)IPlayer.GetOffset(),2) && (GetTickCount() / 1000) % 5 == 0 && IPlayer.IsBuff(BFBuffT2))
	//{
	//	Protect32::RedScore += 1;
	//	IPlayer.Teleport(BFMap,BFRedx1+CTools::Rate(-50,50),BFRedy1+CTools::Rate(-50,50));
	//	IPlayer.Revive();
	//}

	////set scores
	//if (Protect32::Active == true && (IPlayer.IsBuff(BFBuffT2) || IPlayer.IsBuff(BFBuffT1)))
	//	IPlayer.Scenario3_3Score(Protect32::Time,Protect32::BlueScore,Protect32::RedScore);




	//// jail system


 //       if (IPlayer.IsOnline() && Jail.count(IPlayer.GetPID())
 //           && Jail.find(IPlayer.GetPID())->second)
 //       {
	//		if(!IPlayer.IsBuff(jailBuff))
	//			IPlayer.Buff(jailBuff,Jail.find(IPlayer.GetPID())->second/1000,0);

 //           if (IPlayer.IsOnline() && IPlayer.GetRectX() > jailRectX1 && IPlayer.GetRectX() < jailRectX2 && IPlayer.GetRectY() > jailRectY1 && IPlayer.GetRectY() < jailRectY2)
 //           {


 //           }else{
	//			IPlayer.Teleport(jailMap, jailBackX, jailBackY);
 //               std::string msg = "You are a prisoner. You will be able to get out of jail in " + Int2String(IPlayer.GetBuffRemain(jailBuff)) + " seconds.";
 //               IPlayer.SystemMessage(msg,TEXTCOLOR_RED);
	//		}

	//		if (Jail.find(IPlayer.GetPID())->second < GetTickCount())
 //           {
	//			Jail.erase(IPlayer.GetPID());
	//			IPlayer.CancelBuff(jailBuff);
	//			IPlayer.Teleport(0, 257620, 259280);
	//			IPlayer.BoxMsg("Your jail time has ended and you have to relog to regain your strength");
	//		}

 //       }



	return CPlayer::Tick(Player);
}