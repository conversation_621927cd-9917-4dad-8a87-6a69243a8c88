﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Exports.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Functions.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Time.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Tools.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="IChar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Interface.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ISkill.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Memory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="IQuest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="IItem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Command.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Timer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Player.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Start.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ItemUse.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="base64.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ReadConfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Summon.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PKKill.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ApplyDamage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Quest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CanAttack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FinalDamage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AutoLearn.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ShadowSlash.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Packet.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CutDownExp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="LevelUp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GenMonster.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CriticalLock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SecureBuffer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MemoryGuard.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Lock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MutexMap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="Exports.def">
      <Filter>Source Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Core.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Tools.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="IChar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Interface.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ISkill.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Memory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="IItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="IQuest.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="base64.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>