#define _WIN32_DCOM
#include <Windows.h>
#include <process.h>
#include <wininet.h>
#include <time.h>
#include <map>
#include <iostream>
#include <fstream>
#include <sstream>
#include <strstream>
#include <sql.h>
#include <sqltypes.h>
#include <sqlext.h>
#include <fcntl.h>
#include <io.h>
#include <list>
#include <vector>
#include <algorithm>
#include "base64.h"
#include "Sha256.h"
#include <iterator>
#include <iomanip>
#include <comdef.h>
#include <wbemidl.h>
#include <filesystem>
#pragma pack(1)
HINSTANCE hL = 0;
FARPROC p[806] = {0};
#pragma warning(disable: 4018)
#pragma warning(disable: 4129)
#pragma warning (disable : 4244)
#pragma warning (disable : 4305)
#pragma warning (disable : 4309)
#pragma warning (disable : 4996)
//char key = '255';
//char newkey = 'Hell';


bool inRange(unsigned low, unsigned high, unsigned x)
{
return (low <= x && x <= high);
}

void Console()
{
	int hCrtIn, hCrtOut;
	FILE *conIn, *conOut;
	AllocConsole();
	hCrtIn = _open_osfhandle ((intptr_t) GetStdHandle(STD_INPUT_HANDLE), _O_TEXT);
	hCrtOut = _open_osfhandle ((intptr_t) GetStdHandle(STD_OUTPUT_HANDLE), _O_TEXT);
	conIn = _fdopen( hCrtIn, "r" );
	conOut = _fdopen( hCrtOut, "w" );
	*stdin = *conIn;
	*stdout = *conOut;
	SetConsoleTitleA("Heth");
}


#include "SecureBuffer.h"
#include "MemoryGuard.h"
#include "SimpleDatabase.h"

// For C++14 compatibility, use namespace alias
namespace fs = std::filesystem;

// Secure file writing helper
bool SecureWriteToFile(const std::string& filename, const std::string& content) {
    try {
        // Validate filename to prevent directory traversal
        fs::path filepath(filename);
        if (filepath.is_absolute() || filepath.string().find("..") != std::string::npos) {
            return false;  // Reject absolute paths and directory traversal attempts
        }

        // Ensure the directory exists
        fs::path dir = filepath.parent_path();
        if (!dir.empty() && !fs::exists(dir)) {
            fs::create_directories(dir);
        }

        // Use secure buffer for content
        SecureCharBuffer secure_content(content.length() + 1);
        secure_content.safe_copy_from(content.c_str(), content.length());

        std::ofstream log(filename, std::ios_base::app | std::ios_base::out);
        if (log.is_open()) {
            log << secure_content.data() << std::endl;
            log.close();
            return true;
        }
    }
    catch (const std::exception&) {
        // Silently fail for security reasons
    }
    return false;
}

void DiscordLog(std::string lines){
    if (lines.length() > 1024) {  // Limit log line length
        lines = lines.substr(0, 1024);
    }
    SecureWriteToFile("./DiscordLogs/Logs.txt", lines);
}

void writeToFile(std::string lines){
    if (lines.length() > 1024) {  // Limit log line length
        lines = lines.substr(0, 1024);
    }
    SecureWriteToFile("./HethCFG/logger.txt", lines);
}

void writePacketToFile(std::string lines){
    if (lines.length() > 2048) {  // Packets can be longer
        lines = lines.substr(0, 2048);
    }
    SecureWriteToFile("./HethCFG/PacketLogs.txt", lines);
}

//std::string encryptDecrypt(std::string toEncrypt)
//{
//	std::string output = toEncrypt;
//
//	for (int i = 0; i < toEncrypt.size(); i++)
//		output[i] = toEncrypt[i] ^ key;
//
//	return output;
//}
//
//std::string newencryptDecrypt(std::string toEncrypt)
//{
//	std::string output = toEncrypt;
//
//	for (int i = 0; i < toEncrypt.size(); i++)
//		output[i] = toEncrypt[i] ^ newkey;
//
//	return output;
//}

enum TextColor
{
	TEXTCOLOR_GENERAL   = RGB( 255, 255, 255 ),
	TEXTCOLOR_INFOMSG   = RGB(  70, 227, 232 ),
	TEXTCOLOR_SHUTDOWN  = RGB( 240, 116,  15 ),
	TEXTCOLOR_ORANGE    = RGB( 255, 128,  64 ),
	TEXTCOLOR_BLUE      = RGB(   0, 128, 255 ),
	TEXTCOLOR_YELLOW    = RGB( 255, 255, 128 ),
	TEXTCOLOR_RED       = RGB( 255,   0,   0 ),
	TEXTCOLOR_PARTY     = RGB( 210,  64,   0 ),
	TEXTCOLOR_GUILD     = RGB(  10, 255, 229 ),
	TEXTCOLOR_ALLIANCE  = RGB( 128, 128, 192 ),
	TEXTCOLOR_GREEN     = RGB(   0, 255,   0 ),
	TEXTCOLOR_DARKGREEN = RGB(   0, 170,   0 ),
	TEXTCOLOR_FAILED    = RGB( 250, 210,   0 ),
	TEXTCOLOR_CLASSMATE = RGB(   0, 128,   0 ),
	TEXTCOLOR_PUPIL     = RGB( 255, 128,  64 ),
	TEXTCOLOR_PINK      = RGB( 255, 155, 255 ),
};

enum NoticeColor
{
	NOTICECOLOR_BLUE		= 0,
	NOTICECOLOR_RED			= 1,
	NOTICECOLOR_ORANGE		= 2,
	NOTICECOLOR_REDCENTER	= 3,
	NOTICECOLOR_WHITE		= 4,
	NOTICECOLOR_REDLONG		= 5,
	NOTICECOLOR_BLUECENTER	= 6,
	NOTICECOLOR_YELLOW		= 7,
	NOTICECOLOR_PINK		= 8,
	NOTICECOLOR_ORANGELONG	= 9,
};

std::string Int2String(int value)
{
	std::stringstream ss;
	ss << value;
	std::string str = ss.str();
	return str;
}

int String2Int(std::string String)
{
	int Integer;
	std::istringstream iss(String);
	iss >> Integer;
	return Integer;
}

// DEPRECATED: Use SecureBuffer instead
// This function is kept for compatibility but should not be used in new code
char *MakeBuffer(const std::string &string)
{
    if (string.length() > 4096) {  // Prevent excessive allocations
        return nullptr;
    }

    try {
        char *return_string = new char[string.length() + 1];
        strcpy_s(return_string, string.length() + 1, string.c_str());
        return return_string;
    }
    catch (const std::bad_alloc&) {
        return nullptr;
    }
}

// Secure alternative using RAII
SecureCharBuffer MakeSecureBuffer(const std::string &string) {
    if (string.length() > 4096) {  // Prevent excessive allocations
        throw std::invalid_argument("String too long");
    }

    SecureCharBuffer buffer(string.length() + 1);
    buffer.safe_copy_from(string.c_str(), string.length());
    buffer[string.length()] = '\0';
    return buffer;
}

std::string GetHexString(char *bt, size_t max_length = 1024)
{
    if (!bt) {
        return "";
    }

    // Use strnlen to prevent buffer overrun
    size_t len = strnlen(bt, max_length);
    if (len == 0) {
        return "";
    }

    std::string s;
    s.reserve(len * 3);  // Pre-allocate space for efficiency

    for (size_t i = 0; i < len; i++)
    {
        unsigned char b = static_cast<unsigned char>(bt[i]);
        int n1 = b & 15;
        int n2 = (b >> 4) & 15;

        if (n2 > 9)
            s += static_cast<char>(n2 - 10 + 'A');
        else
            s += static_cast<char>(n2 + '0');

        if (n1 > 9)
            s += static_cast<char>(n1 - 10 + 'A');
        else
            s += static_cast<char>(n1 + '0');

        if ((i + 1) != len && (i + 1) % 4 == 0) s += "-";
    }

    return s;
}

// Secure version using SecureBuffer
std::string GetHexStringSecure(const SecureByteBuffer& buffer)
{
    if (buffer.empty()) {
        return "";
    }

    std::string s;
    s.reserve(buffer.size() * 3);

    for (size_t i = 0; i < buffer.size(); i++)
    {
        unsigned char b = buffer[i];
        int n1 = b & 15;
        int n2 = (b >> 4) & 15;

        if (n2 > 9)
            s += static_cast<char>(n2 - 10 + 'A');
        else
            s += static_cast<char>(n2 + '0');

        if (n1 > 9)
            s += static_cast<char>(n1 - 10 + 'A');
        else
            s += static_cast<char>(n1 + '0');

        if ((i + 1) != buffer.size() && (i + 1) % 4 == 0) s += "-";
    }

    return s;
}



void saveKilled(int minionIndex, int minionAmount) {
    // Validate input parameters
    if (minionIndex < 0 || minionIndex > 99999 || minionAmount < 0 || minionAmount > 999999) {
        writeToFile("Invalid parameters in saveKilled: minionIndex=" + Int2String(minionIndex) + ", minionAmount=" + Int2String(minionAmount));
        return;
    }

    // Use the new simple database system with helper function
    if (!SaveToSystemDB("killtosummon", minionIndex, minionAmount)) {
        writeToFile("Error in saveKilled: Failed to save to database for minionIndex=" + Int2String(minionIndex));
    }
}
namespace Protect32
{
	int Time = 0;
	bool Active = false;
	std::string FirstGuild = "";
	std::string SecondGuild = "";
	int GuildFirst = 0;
	int GuildSecond = 0;
	int Prayer = 0;
	int RedScore = 0;
	int BlueScore = 0;
	int RedWin = 0;
	int BlueWin = 0;
	int Winner = 0;
	int Round = 0;
	int BluePrayerTime = 0;
	int RedPrayerTime = 0;
}

namespace Muta
{
	bool Active = false;
	bool BrotherDead = false;
	bool BrotherSeek = false;
	int RegisterAmount = 0;
	int ShowTime = 0;
	int Dialogue = 0;
}

namespace Raid
{
	bool Active = false;
	bool SummonSkillUsed = false;
	int RegisterAmount = 0;
	int ShowTime = 0;
	int KillCount = 0;
	int lastRaidBossSkillDelay = 0;
	int MinionsKillCount = 0;

}

struct ConfigBossDrops
{
int bossIndex;
int rngdrops;
int shownotice;
int level;
int bound;
unsigned __int64 exp;
int money;
int droprange;
	std::vector<int> Items;
	std::vector<int> Amounts;
	std::vector<int> Rates;
};
struct ConfigrBossDrops
{
int rbossIndex;
int rlevel;
int rbound;
int playerAmount;
int ovRate;
int rdroprange;
std::vector<int> Items;
std::vector<int> Amounts;
std::vector<int> Rates;
};


struct ConfigOpenHtml{
	int Index;
	int Html;
};

struct ConfigInvEx{
	int iteminvindex;
	int invexcd;
};

struct ConfigMonsterDebuff
{
	int animationID;
	int animationDelay;
	int AoE;
	int monsterIndex;
	int debuffhp;
	int debuffrange;
	int debuffid;
	int debuffcd;
	int debuffval;
	int excludedclass;
	int skilldelay;
	int debuffdelay;

};

struct ConfigMonsterSkill
{
	int animationID;
	int animationDelay;
	int AoE;
	int monsterSkillIndex;
	int skillhpmin;
	int skillhpmax;
	int skilldamagemin;
	int skilldamagemax;
	int skillrange;
	int mskilldelay;
	int deskilldelay;

};
struct Configkts
{
	int minionIndex;
	int minionAmount;
	int bossIndex;
	int bossAmount;
	int spawnX;
	int spawnY;
	int spawnMap;
	int totalKilled;

};
struct ConfigMonsterSummon
{
	int animationID;
	int animationDelay;
	int AoE;

	int monsterSummonIndex;
	int summonhpmin;
	int summonhpmax;
	int summonMinionIndex;
	int summonMinionAmount;
	int summonrange;
	int smnskilldelay;
	int desmnskilldelay;

};

struct ConfigAreaProtection
{
	int apindex;
	int aplevel;
	int apmap;
	int aprectX1;
	int aprectY1;
	int aprectX2;
	int aprectY2;
	int aptpX;
	int aptpY;

};

struct ConfigBossD
{
int questIndex;
int itemIndex;
int itemAmount;
int monsterIndex;
int monsterAmount;
 int X;
  int Y;
   int Map;
   int totalCollected;
};

struct ConfigBoxes
{
	int BoxIsBound;
	int BoxIndex;
	std::vector<int> Items;
	std::vector<int> Amounts;
};
struct ConfigRNGBoxes
{
	int RNGBoxIsBound;
	int RNGBoxIndex;
	int donotice;
	std::vector<int> Items;
	std::vector<int> Amounts;
	std::vector<int> Rates;

};
struct ConfigFishing
{
	int bound;
	int exp;
	int money;
	int level;
	std::vector<int> Items;
	std::vector<int> Amounts;
	std::vector<int> Rates;

};
struct ConfigRandomBoxes
{
	int RandomBoxIsBound;
	int RandomBoxIndex;
	std::vector<int> Items;
	std::vector<int> Amounts;

};
struct ConfigBossTanker
{
	int bossIndex;
	int cd;
	int effectCD;
};
struct ConfigDisableQuest
{
	int buffid;
	std::string message; 
};
struct ConfigItemBuff
{
	int itemIndex;
	int buffid;
	int buffcd;
	int buffval;
};

struct afk{
	int cd;
	int x;
	int y;
	bool Warning;
};

struct ConfigQueenTopDamage
{	
	int Dmg;
};

struct ConfigItemBlock
{	
	int index;
	int disabled;
};

struct ConfigPacketSpam {
	int packet;
	int cooldown;
};


struct ConfigNewMonsterSkill {
	int monsterIndex;
	std::vector<int> mSkillIDs;  // Changed from single int to vector for multiple skill IDs
	int CD;
	int Delay;
	int Damage;

	// Helper method to get a random skill ID
	int getRandomSkillID() const {
		if (mSkillIDs.empty()) return 0;
		if (mSkillIDs.size() == 1) return mSkillIDs[0];

		// Use simple random selection
		int randomIndex = rand() % mSkillIDs.size();
		return mSkillIDs[randomIndex];
	}
	int DamageMin;
	int DamageMax;
	int AoE;
	int EffectType;
	std::string Effect;
};


time_t uptimestart; int BofConfigRead = 0, ImpConfigRead = 0, TotalBlock = 0, NailKill = 0;
int NPCEDailyLimit = 0, NPCECollectedTotalItem = 0, NPCELeftTime = 0; char ItemShopPacket[9000] = {0};
#define HIDWORD(l) ((DWORD)(((DWORDLONG)(l)>>32)&0xFFFFFFFF))

std::map<DWORD, afk> afkMap;


std::map<int,ConfigNewMonsterSkill> NewMonsterSkill;
std::map<int,int> QueenTopDmg;
std::map<int,ConfigBoxes> BoxesCheck;
std::map<int,ConfigBossTanker> BossTanker;
std::map<int,ConfigDisableQuest> DisableQuest;
std::map<int,std::string> BossTankerFX;
std::map<int,std::string> BossTankerBName;
std::map<int,ConfigBossDrops> BDSCheck;
std::map<int,ConfigrBossDrops> rBDSCheck;
std::map<int,ConfigItemBuff> ItemBuffCheak;
std::map<int,int> RaidRegistration;
std::map<int,ConfigRNGBoxes> RNGBoxesCheck;
std::map<int,ConfigFishing> FishingCheck;
std::map<int,ConfigRandomBoxes> RandomBoxesCheck;
std::map<int,std::string> RNGBoxesName;
std::map<int,std::string> BossName;
std::map<int,std::string> rBDSCheckMsg;
std::map<int,std::string> MonsterSkillFx;
std::map<int,std::string> MonsterBuffFx;
std::map<int,std::string> MonsterSummonFx;
std::map<int,ConfigInvEx> ItemInvExCheak;
std::map<int,DWORD> Jail;
std::map<int,void*> SummonAi;
std::map<int,DWORD> MonsterDisappear;
std::map<std::string,std::string> AutoBattleDay;
std::map<std::string,std::string> AutoBattleType;
std::map<int,std::string> bossDonationMsg;
std::map<int,std::string> KillToSummonBossName;
std::map<int,std::string> KillToSummonMinionName;
std::map<int,int> MutaRegistration;
std::vector<int> ProtectLeaderList;
std::map<int,std::string> ProtectLeaderName;
std::map<int,ConfigMonsterDebuff> MonsterBuffCheak;
std::map<int,ConfigMonsterSkill> MonsterSkillCheak;
std::map<int,ConfigMonsterSummon> MonsterSummonCheak;
std::map<int,ConfigAreaProtection> AreaProtectionCheck;
std::map<int,ConfigBossD> BossDonation;
std::map<int,ConfigOpenHtml> openHtml;
std::map<int,ConfigItemBlock> ItemBlock;
std::map<int,Configkts> KillToSummon;
std::map<int,ConfigPacketSpam> PacketSpam;

#pragma comment(lib,"Detours/enigma_ide.lib")
#pragma comment(lib,"Detours/detours.lib")
#pragma comment(lib,"Ws2_32.lib")
#pragma comment(lib,"Wininet.lib")
#pragma comment(lib,"netapi32.lib")
#pragma comment(lib,"wbemuuid.lib")
#include <stdio.h>
#include <lm.h>
#include <tchar.h>
#include "Detours/enigma_ide.h"
#include "Detours/detours.h"
#include "Memory.h"
#include "Tools.h"
#include "Interface.h"
#include "Time.h"
#include "Functions.h"
#include "ReadConfig.h"
#include "ISkill.h"
#include "IChar.h"
#include "IItem.h"
#include "IQuest.h"
#include "Player.h"
#include "Summon.h"
#include "GenMonster.h"
#include "Command.h"
#include "ItemUse.h"
#include "Start.h"
//#include "PKKill.h"
//#include "ApplyDamage.h"
#include "Quest.h"
//#include "CanAttack.h"
#include "FinalDamage.h"
//#include "LevelUp.h"
//#include "AutoLearn.h"
//#include "CutDownExp.h"
//#include "Packet.h"
#include "Timer.h"

//DWORD owner = 2101229216;

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
	
//		TCHAR volumeName[MAX_PATH + 1] = { 0 };
//	TCHAR fileSystemName[MAX_PATH + 1] = { 0 };
//	DWORD serial = 0;
//	DWORD maxComponentLen = 0;
//	DWORD fileSystemFlags = 0;
//	if (GetVolumeInformation(
//		_T("C:\\"),
///		volumeName,
//		ARRAYSIZE(volumeName),
///		&serial,
//		&maxComponentLen,
//	&fileSystemFlags,
//	fileSystemName,
//		ARRAYSIZE(fileSystemName)))
//	{
//		 if (owner != serial*150+10) {
//		MessageBox(0, "You dont have access to Heth Addons", "Heth", MB_OK | MB_ICONINFORMATION);
//		 return false;
 //   }
		
//	}


	switch (ul_reason_for_call)
	{
	case DLL_PROCESS_ATTACH:
		{

			ReadConfig();
			DetourTransactionBegin();
			DetourAttach(&(PVOID&)CIOServer::Start, Start);
			DetourAttach(&(PVOID&)CPlayer::ChatCommand, ChatCommand);
			DetourAttach(&(PVOID&)CCalendar::OnTimer, OnTimer);
			DetourAttach(&(PVOID&)CItemGeneral::Use, ItemUse);
			DetourAttach(&(PVOID&)CMonsterMaguniMaster::AI, SummonAI);
			DetourAttach(&(PVOID&)CMonsterMaguniMaster::Die, SummonDie);
			DetourAttach(&(PVOID&)CMonsterMaguniMaster::Tick, SummonTick);
			DetourAttach(&(PVOID&)CMonsterGenMonster::Die, GenMonsterDie);
			DetourAttach(&(PVOID&)CMonsterReal::Tick, GenMonsterTick);
			//DetourAttach(&(PVOID&)CPlayer::CanAttack, CanAttack);
			DetourAttach(&(PVOID&)CChar::GetFinalDamage, GetFinalDamage);
			//DetourAttach(&(PVOID&)CPlayer::LevelUp, LevelUp);
			//DetourAttach(&(PVOID&)CPlayer::PKKill, PKKill);
			DetourAttach(&(PVOID&)CConsole::Black, Black);
			DetourAttach(&(PVOID&)CConsole::Blue, Blue);
			DetourAttach(&(PVOID&)CPlayer::Tick, Tick);
			DetourAttach(&(PVOID&)CQuest::Run, Quest);
			
			
			//DetourAttach(&(PVOID&)CMonsterReal::ApplyDamage, ApplyDamage);
			//DetourAttach(&(PVOID&)CSkill::Occupation, Occupation);
			DetourAttach(&(PVOID&)CPlayer::GameStart, MyGameStart);
			//DetourAttach(&(PVOID&)CPlayer::CutdownExp, CutdownExp);
			//DetourAttach(&(PVOID&)CPlayer::LevelUpUnknown, AutoLearn);
			


			DetourTransactionCommit();
			break;
		}
	case DLL_PROCESS_DETACH:
		{
			ReadConfig();
			DetourTransactionBegin();
			DetourDetach(&(PVOID&)CIOServer::Start, Start);
			DetourDetach(&(PVOID&)CPlayer::ChatCommand, ChatCommand);
			DetourDetach(&(PVOID&)CCalendar::OnTimer, OnTimer);
			DetourDetach(&(PVOID&)CItemGeneral::Use, ItemUse);
			DetourDetach(&(PVOID&)CMonsterMaguniMaster::AI, SummonAI);
			DetourDetach(&(PVOID&)CMonsterMaguniMaster::Die, SummonDie);
			DetourDetach(&(PVOID&)CMonsterMaguniMaster::Tick, SummonTick);
			DetourDetach(&(PVOID&)CMonsterGenMonster::Die, GenMonsterDie);
			DetourDetach(&(PVOID&)CMonsterReal::Tick, GenMonsterTick);
			
			//DetourDetach(&(PVOID&)CPlayer::CanAttack, CanAttack);
			DetourDetach(&(PVOID&)CChar::GetFinalDamage, GetFinalDamage);
			//DetourDetach(&(PVOID&)CPlayer::LevelUp, LevelUp);
			//DetourDetach(&(PVOID&)CPlayer::PKKill, PKKill);
			DetourDetach(&(PVOID&)CConsole::Black, Black);
			DetourDetach(&(PVOID&)CConsole::Blue, Blue);
			DetourDetach(&(PVOID&)CPlayer::Tick, Tick);
			DetourDetach(&(PVOID&)CQuest::Run, Quest);
			//DetourDetach(&(PVOID&)CMonsterReal::ApplyDamage, ApplyDamage);
			//DetourDetach(&(PVOID&)CSkill::Occupation, Occupation);
			DetourDetach(&(PVOID&)CPlayer::GameStart, MyGameStart);
			//DetourDetach(&(PVOID&)CPlayer::CutdownExp, CutdownExp);
			//DetourDetach(&(PVOID&)CPlayer::LevelUpUnknown, AutoLearn);




			DetourTransactionCommit();
			break;
		}
	}
	return TRUE;
}