
void __fastcall OnTimer(void *Value, void *edx, int Argument)
{
	CCalendar::OnTimer(Value,Argument);
	time_t MyTime = time(0);
	tm *DuelTime = localtime(&MyTime);

	//if (Protect32::RedScore > 999)
	//{
	//	Protect32::RedScore = 999;
	//	Protect32::Time = 0;
	//}

	//if (Protect32::BlueScore > 999)
	//{
	//	Protect32::BlueScore = 999;
	//	Protect32::Time = 0;
	//}

	//if (Protect32::Active == true && Protect32::Time > 0)
	//	Protect32::Time -= 1;


	//if (Protect32::Active == true && Protect32::Time == 0)
	//{

	//	if (Protect32::Active == true && Protect32::RedScore < Protect32::BlueScore)
	//	{
	//		Protect32::Winner = Protect32::GuildSecond;
	//		std::string msg = Protect32::SecondGuild + " won the GvG Battlefield.";
	//		CPlayer::WriteAll(0xFF, "dsd", 247, msg.c_str(), 2);
	//		Protect32::Active = false;
	//	}

	//	if (Protect32::Active == true && Protect32::RedScore > Protect32::BlueScore)
	//	{
	//		Protect32::Winner = Protect32::GuildFirst;
	//		std::string msg = Protect32::FirstGuild + " won the GvG Battlefield.";
	//		CPlayer::WriteAll(0xFF, "dsd", 247, msg.c_str(), 2);
	//		Protect32::Active = false;
	//	}

	//	if (Protect32::Active == true && Protect32::RedScore == Protect32::BlueScore){
	//			Protect32::Winner = 0;
	//			std::string msg = "GvG Battlefield between " + Protect32::FirstGuild + " guild and " + Protect32::SecondGuild + " guild ended in draw.";
	//			CPlayer::WriteAll(0xFF, "dsd", 247, msg.c_str(), 2);
	//			Protect32::Active = false;
	//		
	//	}
	//}


	//Auto Battles
	if (AutoBattleDay.count(Time::GetTime()) && AutoBattleType.count(Time::GetTime()))
	{
		//check day
		if (AutoBattleDay.find(Time::GetTime())->second == Time::GetDayName() || AutoBattleDay.find(Time::GetTime())->second == "Daily"){
			//mautareta
			//if (AutoBattleType.find(Time::GetTime())->second == "mautareta" || AutoBattleType.find(Time::GetTime())->second == "Mautareta"){
			//	if (Muta::RegisterAmount > 0)
			//		{
			//			Muta::Dialogue = 0;
			//			Muta::RegisterAmount = 0;
			//			Muta::Active = true;
			//			CPlayer::WriteAll(0xFF, "dsd", 247, "Mautareta System has started.", 2);
			//			Summon(0, mutaMap, mutaSpawnX, mutaSpawnY, mutaIndex, 1, 0, 0, mutaTime*1000, 0);
			//		} else {
			//			CPlayer::WriteAll(0xFF, "dsd", 247, "Not enough players registered for the Mautareta system.", 2);
			//		}
			//}


			//raid
			if (AutoBattleType.find(Time::GetTime())->second == "Raid" || AutoBattleType.find(Time::GetTime())->second == "raid"){
				if (Raid::RegisterAmount > 0)
					{
						Raid::KillCount = 0;
						Raid::RegisterAmount = 0;
						Raid::Active = true;
						CPlayer::WriteAll(0xFF, "dsd", 247, "Raid System has started.", 2);
						Summon(0, raidMap, raidManagerX, raidManagerY, raidManager, 1, 0, raidTime*1000, raidTime*1000, 0);
					} else {
						CPlayer::WriteAll(0xFF, "dsd", 247, "Not enough players registered for the raid system.", 2);
					}
			}
			//gvgbf
			//if (AutoBattleType.find(Time::GetTime())->second == "gvgbf" || AutoBattleType.find(Time::GetTime())->second == "GvGBF"){
			//	if (ProtectLeaderList.size() > 1)
			//		{
			//			std::random_shuffle(ProtectLeaderList.begin(), ProtectLeaderList.end());
			//			Protect32::GuildFirst = ProtectLeaderList.front();
			//			ProtectLeaderList.erase(ProtectLeaderList.begin());
			//			std::random_shuffle(ProtectLeaderList.begin(), ProtectLeaderList.end());
			//			Protect32::GuildSecond = ProtectLeaderList.front();
			//			ProtectLeaderList.erase(ProtectLeaderList.begin());		
			//			Protect32::FirstGuild = ProtectLeaderName.find(Protect32::GuildFirst)->second;
			//			Protect32::SecondGuild = ProtectLeaderName.find(Protect32::GuildSecond)->second;
			//			ProtectLeaderList.clear();
			//			ProtectLeaderName.clear();		
			//			std::string msg = "GvG Battlefield started between ";
			//			msg = msg + Protect32::FirstGuild;
			//			msg = msg + " guild and ";
			//			msg = msg + Protect32::SecondGuild;
			//			msg = msg + " guild.";
			//			CPlayer::WriteAll(0xFF, "dsd", 247, msg.c_str(), 2);
			//			Protect32::RedScore = 0;
			//			Protect32::BlueScore = 0;
			//			Protect32::RedWin = 0;
			//			Protect32::BlueWin = 0;
			//			Protect32::Winner = 0;
			//			Protect32::Round = 1;
			//			Protect32::Time = BFTime;
			//			Protect32::Active = true;
			//		} else {
			//			CPlayer::WriteAll(0xFF, "dsd", 247, "Not enough players registered for GvG Battlefield.", 2);
			//		}
			//	}
			//other

		}
	}



	// if (Time::GetTime() == "19:00:00" && (Time::GetDayName() == "Monday" || Time::GetDayName() == "Wednesday" || Time::GetDayName() == "Friday"))
	// {
	// 	if (Raid::RegisterAmount > 0)
	// 	{
	// 		Raid::KillCount = 0;
	// 		Raid::RegisterAmount = 0;
	// 		Raid::Active = true;
	// 		CPlayer::WriteAll(0xFF, "dsd", 247, "Raid System has started.", 2);
	// 		Summon(0, raidMap, raidManagerX, raidManagerY, raidManager, 1, 0, raidTime*1000, raidTime*1000, 0);
	// 	} else {
	// 		CPlayer::WriteAll(0xFF, "dsd", 247, "Not enough players registered in the raid system.", 2);
	// 	}
	// }



	// 	if (Protect32::Active == false && Time::GetTime() == "21:00:00" && (Time::GetDayName() == "Monday" || Time::GetDayName() == "Wednesday" || Time::GetDayName() == "Friday"))
	// {
	// 		if (ProtectLeaderList.size() > 1)
	// 		{
	// 			std::random_shuffle(ProtectLeaderList.begin(), ProtectLeaderList.end());
	// 			Protect32::GuildFirst = ProtectLeaderList.front();
	// 			ProtectLeaderList.erase(ProtectLeaderList.begin());
	// 			std::random_shuffle(ProtectLeaderList.begin(), ProtectLeaderList.end());
	// 			Protect32::GuildSecond = ProtectLeaderList.front();
	// 			ProtectLeaderList.erase(ProtectLeaderList.begin());		
	// 			Protect32::FirstGuild = ProtectLeaderName.find(Protect32::GuildFirst)->second;
	// 			Protect32::SecondGuild = ProtectLeaderName.find(Protect32::GuildSecond)->second;
	// 			ProtectLeaderList.clear();
	// 			ProtectLeaderName.clear();		
	// 			std::string msg = "GvG Battlefield started between ";
	// 			msg = msg + Protect32::FirstGuild;
	// 			msg = msg + " guild and ";
	// 			msg = msg + Protect32::SecondGuild;
	// 			msg = msg + " guild.";
	// 			CPlayer::WriteAll(0xFF, "dsd", 247, msg.c_str(), 2);
	// 			Protect32::RedScore = 0;
	// 			Protect32::BlueScore = 0;
	// 			Protect32::RedWin = 0;
	// 			Protect32::BlueWin = 0;
	// 			Protect32::Winner = 0;
	// 			Protect32::Round = 1;
	// 			Protect32::Time = BFTime;
	// 			Protect32::Active = true;
	// 		} else {
	// 			CPlayer::WriteAll(0xFF, "dsd", 247, "Not enough players registered for GvG Battlefield.", 2);
	// 		}
	// }

}