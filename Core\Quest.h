void __fastcall Quest(void *QuestOffset, void *edx, int PlayerOffset)
{
	IQuest Quest(QuestOffset);
	IChar IPlayer((void*)PlayerOffset);

	// dont teleport when in pvp systems
	if(IPlayer.IsOnline() &&
	 (IPlayer.IsBuff(raidBuff))){

		IPlayer.BoxMsg("You cant teleport because you are inside a system.");
		return;
	}

	if(IPlayer.IsOnline() && IPlayer.GetCurHp() == 0){
		//IPlayer.SystemMessage("You cant teleport because you are inside a system.", TEXTCOLOR_RED);
		IPlayer.BoxMsg("u ded bro? u cant teleport.");
		return;
	}

		//Fish Teleporter
	/*if (IPlayer.IsOnline() && FishTeleportQuest == Quest.GetIndex())
	{
		IPlayer.SystemMessage("Happy Fishing!",TEXTCOLOR_CLASSMATE);
		int placeNumber = CTools::Rate(1,4);
		switch(placeNumber) {
		  case 1:
			IPlayer.Teleport(0,257223,257414);
			break;
		  case 2:
			IPlayer.Teleport(0,258273,264887);
			break;
		  case 3:
			IPlayer.Teleport(0,261530, 262033);
			break;
		  case 4:
			IPlayer.Teleport(0,265328, 276963);
			break;
		  default:
			  IPlayer.Teleport(0,267440, 242250);
		}

		
		return;
	}*/


	//Boss Donations
// 	if (IPlayer.IsOnline() && BossDonation.find(Quest.GetIndex()-1)->second.questIndex+1 == Quest.GetIndex())
// 	{
// 		std::string msg = Int2String(BossDonation.find(Quest.GetIndex()-1)->second.totalCollected) + " items has been collected!";
// 		IPlayer.SystemMessage(msg,TEXTCOLOR_PINK);
// 		return;
// 	}

// if (IPlayer.IsOnline() && BossDonation.find(Quest.GetIndex())->second.questIndex == Quest.GetIndex())
// 	{
// 		int ItemCheck = CPlayer::FindItem(IPlayer.GetOffset(),BossDonation.find(Quest.GetIndex())->second.itemIndex,1);

// 		if (!ItemCheck)
// 		{
// 			IPlayer.SystemMessage("You do not have the required item!",TEXTCOLOR_RED);
// 			return;
// 		}
// 		if (ItemCheck)
// 			{
// 				IItem IItem((void*)ItemCheck);
// 				int CheckItem = 0, CheckItemLeft = BossDonation.find(Quest.GetIndex())->second.itemAmount - BossDonation.find(Quest.GetIndex())->second.totalCollected;

// 				if (IItem.GetAmount() > CheckItemLeft){
// 					CheckItem = CheckItemLeft;
// 				}
// 				else{
// 					CheckItem = IItem.GetAmount() - 1;
// 				}


// 				if(CheckItem > 0){
// 				BossDonation.find(Quest.GetIndex())->second.totalCollected += CheckItem;
// 				(*(int (__thiscall **)(DWORD, void *, signed int, signed int))(*(DWORD*)ItemCheck + 120))((int)ItemCheck,IPlayer.GetOffset(),9,-CheckItem);
				
// 				std::string msg = Int2String(CheckItem) + " items has been collected from you.";
// 				IPlayer.SystemMessage(msg,TEXTCOLOR_GREEN);
// 				//IPlayer.SystemMessage("Relog recommended after donating items to avoid inventory bug.",TEXTCOLOR_RED);
				
// 				std::string playerName = IPlayer.GetName();
// 				std::string msgall =  playerName + " has donated " + Int2String(CheckItem) + "x items, to summon " + bossDonationMsg.find(Quest.GetIndex())->second+", "+ Int2String(BossDonation.find(Quest.GetIndex())->second.totalCollected) + "/"+Int2String(BossDonation.find(Quest.GetIndex())->second.itemAmount)+" items has been collected in total";
// 				CPlayer::WriteAll(0xFF, "dsd", 247, msgall.c_str(),0);
				
// 				saveCollected(BossDonation.find(Quest.GetIndex())->second.monsterIndex,BossDonation.find(Quest.GetIndex())->second.totalCollected);
// 				}else{
// 					IPlayer.BoxMsg("You cant donate all of your items.");
// 				}
// 			}

// 			if (BossDonation.find(Quest.GetIndex())->second.totalCollected == BossDonation.find(Quest.GetIndex())->second.itemAmount)
// 			{
// 				int monsterIndex = BossDonation.find(Quest.GetIndex())->second.monsterIndex;
// 					int monsterMap = BossDonation.find(Quest.GetIndex())->second.Map;
// 					int monsterX = BossDonation.find(Quest.GetIndex())->second.X;
// 					int monsterY = BossDonation.find(Quest.GetIndex())->second.Y;
// 					int monsterAmount = BossDonation.find(Quest.GetIndex())->second.monsterAmount;
// 					std::string msgall = bossDonationMsg.find(Quest.GetIndex())->second + " has been summoned by the players.";
// 					CPlayer::WriteAll(0xFF, "dsd", 247, msgall.c_str(),7);
// 					if(monsterAmount == 0 || monsterAmount == 1){
// 						Summon(0, monsterMap, monsterX, monsterY, monsterIndex, 1, 0, 0, 3600000, 0);
// 					}else{
// 					for (int x = 0; x < monsterAmount; x++) {
// 						Summon(0, monsterMap, monsterX, monsterY, monsterIndex, 1, 0, 0, 3600000, 0);
//            			 }
// 					}
					
					
// 				BossDonation.find(Quest.GetIndex())->second.totalCollected = 0;
// 				saveCollected(BossDonation.find(Quest.GetIndex())->second.monsterIndex,BossDonation.find(Quest.GetIndex())->second.totalCollected);
				
// 			}


// 		return;
// 	}





		//Raid
	if(IPlayer.IsOnline() && Quest.GetIndex() == raidQuest)
	{
		if(IPlayer.GetLevel() >= raidLevel){
		
		if (Raid::Active == true)
		{
			IPlayer.SystemMessage("You can not register while the Raid System is running.",TEXTCOLOR_RED);
			return;
		}

		if (RaidRegistration.find(IPlayer.GetPID())->second)
		{
			IPlayer.SystemMessage("You already registered to the Raid System.",TEXTCOLOR_RED);
			return;
		} else {
			RaidRegistration[IPlayer.GetPID()] = 1;
			Raid::RegisterAmount += 1;
			IPlayer.SystemMessage("Successfully registered to the Raid System.",TEXTCOLOR_GREEN);
			return;
		}

	}else{
		IPlayer.SystemMessage("Check the required level.",TEXTCOLOR_RED);
		return;
	}

		return;
	}
	

		//Mautareta
	//if(IPlayer.IsOnline() && Quest.GetIndex() == mutaQuest)
	//{
	//	if(IPlayer.GetLevel() >= mutaLevel){

	//	int ItemCheck = CPlayer::FindItem(IPlayer.GetOffset(),mutaItem,1);
	//	if(!ItemCheck){
	//		IPlayer.SystemMessage("Check the required item.",TEXTCOLOR_RED);
	//		return;
	//	}
	//	
	//	if (Muta::Active == true)
	//	{
	//		IPlayer.SystemMessage("You can not register while the Mautareta System is running.",TEXTCOLOR_RED);
	//		return;
	//	}

	//	if (MutaRegistration.find(IPlayer.GetPID())->second)
	//	{
	//		IPlayer.SystemMessage("You already registered to the Mautareta System.",TEXTCOLOR_RED);
	//		return;
	//	} else {
	//		MutaRegistration[IPlayer.GetPID()] = 1;
	//		Muta::RegisterAmount += 1;
	//		IPlayer.SystemMessage("Successfully registered to the Mautareta System.",TEXTCOLOR_GREEN);
	//		return;
	//	}

	//}else{
	//	IPlayer.SystemMessage("Check the required level.",TEXTCOLOR_RED);
	//	return;
	//}

	//	return;
	//}


	//GvGBF
	//if (IPlayer.IsOnline() && Quest.GetIndex() == BFQuest)
	//{
	//	if (IPlayer.IsOnline() && Protect32::Active == true)
	//	{
	//		IPlayer.SystemMessage("You can not register while GvG Battlefield is running.",TEXTCOLOR_RED);
	//		return;
	//	}

	//	if (IPlayer.IsOnline() && IPlayer.GetPID() != IPlayer.GetGID())
	//	{
	//		IPlayer.SystemMessage("Only guild leader can register for GvG Battlefield.",TEXTCOLOR_RED);
	//		return;
	//	}

	//	if (IPlayer.IsOnline() && ProtectLeaderName.count(IPlayer.GetGID()))
	//	{
	//		IPlayer.SystemMessage("You already registered to GvG Battlefield.",TEXTCOLOR_RED);
	//		return;
	//	} else {

	//		int PlayerGuild = CPlayer::GetGuildName(PlayerOffset);
	//		if (IPlayer.IsOnline() && PlayerGuild)
	//		{
	//			ProtectLeaderName[IPlayer.GetGID()] = (char*)CPlayer::GetGuildName(PlayerOffset);
	//			ProtectLeaderList.push_back(IPlayer.GetGID());
	//			IPlayer.SystemMessage("Successfully registered to GvG Battlefield.",TEXTCOLOR_GREEN);
	//		} else {
	//			IPlayer.SystemMessage("Guild level is low for GvG Battlefield registration.",TEXTCOLOR_RED);
	//		}
	//	}

	//	return;
	//}





	//PvP A
	//	if(IPlayer.IsOnline() && Quest.GetIndex() == pvpAreaQuestID)
	//{
	//	if(IPlayer.GetLevel() >= pvpAreaLevel){
	//		if(IPlayer.GetMap() == pvpAreaMap){
	//			IPlayer.Teleport(0,267781,242794);
	//		}else{
	//			IPlayer.Teleport(pvpAreaMap,pvpAreaTeleX,pvpAreaTeleY);
	//		}
	//		
	//		
	//}else{
	//	IPlayer.SystemMessage("Check the required level.",TEXTCOLOR_RED);
	//	return;
	//}

	//	return;
	//}

	CQuest::Run(QuestOffset, PlayerOffset);
}