int __fastcall LevelUp(void *Player, void *edx)
{
	IChar IPlayer(Player);
	int result = 0;

	if (IPlayer.IsOnline())
		result = CPlayer::LevelUp(Player);

	if (IPlayer.IsOnline() && IPlayer.GetLevel() > LvlLimitNotice && IPlayer.GetAdmin() == 0)
	 {
	 	std::string name = IPlayer.GetName();
	 	std::string msg = name + " has leveled up " + Int2String(IPlayer.GetLevel()) + " congratulations.";
		CPlayer::WriteAll(0xFF, "dsd", 247, msg.c_str() , LvlColorNotice);
	 }



	return result;
}