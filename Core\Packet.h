void __fastcall Packet(__int32 Player, void *edx, int packet, void *pPacket,
    int pPos)
{
    IChar IPlayer((void*)Player);

    //CConsolef::Red("Packet Received ID: %d", packet);

	if (IPlayer.IsOnline() && IPlayer.GetPID())
    {

         


           //if (packet == 37 && (IPlayer.IsBuff(BFBuffT1) || IPlayer.IsBuff(BFBuffT2))) {
            //return;
        //}
			CPlayer::Process((void*)Player, packet, (void*)pPacket, pPos);
    }

}
